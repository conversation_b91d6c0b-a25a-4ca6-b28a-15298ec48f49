import time
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
from typing import Dict, Callable
from lib.pattern.sampackag.db_handler.db_connector import (
    MPISetupTableResult,
    MPIOpcodesTableResult,
    menuMasterTableResult,
    menuServiceTypeTableResult,
    assignedMenuModelsTableResult,
    assignedMenuOpcodesTableResult,
    allRevenueDetailsTable,
    getCustomerPayTypeGroupsList,
    allRevenueDetailsForClientReportCard3Month,
)
from lib.pattern.config import config

class RobustDataLoader:
    """Data loader with error handling and retry logic"""
    
    def __init__(self, max_workers=5, max_retries=3):
        self.max_workers = max_workers
        self.max_retries = max_retries
        self.results = {}
        self.failed_tasks = []
        self.task_timings = {}
    
    def load_all_data_with_retry(self):
        """Load data with retry logic for failed queries"""

        print("Loading database data")
        start_time = time.time()
        
        # Critical vs non-critical data classification
        critical_tasks = {
            'all_revenue_details': lambda: allRevenueDetailsTable().getTableResult(),
            'retail_flag_all': lambda: getCustomerPayTypeGroupsList().getCustomerPayTypeList(),
        }
        
        non_critical_tasks = {
            'menu_master_df': lambda: menuMasterTableResult().getTableResult(),
            'menu_service_type_df': lambda: menuServiceTypeTableResult().getTableResult(),
            'assigned_menu_models_df': lambda: assignedMenuModelsTableResult().getTableResult(),
            'assigned_menu_opcodes_df': lambda: assignedMenuOpcodesTableResult().getTableResult(),
            'mpi_setup_df': lambda: MPISetupTableResult().getTableResult(),
            'mpi_opcodes': lambda: MPIOpcodesTableResult().getTableResult(),
            'all_revenue_details_for_client_report_card_3_month': lambda: allRevenueDetailsForClientReportCard3Month().getTableResult(),
        }
        
        # Load critical data first
        print("Loading critical data...")
        self._load_task_group(critical_tasks, max_workers=2)
        
        # Check if critical data loaded successfully
        critical_failed = [k for k in critical_tasks.keys() if self.results.get(k) is None]
        if critical_failed:
            print(f"Critical data failed to load: {critical_failed}")
            # Retry critical data
            retry_tasks = {k: critical_tasks[k] for k in critical_failed}
            self._load_task_group_with_retry(retry_tasks)
        
        # Load non-critical data
        print("Loading non-critical data...")
        self._load_task_group(non_critical_tasks, max_workers=self.max_workers)
        
        self._assign_to_config()
        
        end_time = time.time()
        print(f"Database loading completed in {end_time - start_time:.2f} seconds")
        
        # Print timing summary
        self._print_timing_summary()
        
        if self.failed_tasks:
            print(f"⚠️  Some non-critical data failed to load: {self.failed_tasks}")
        
        return self.results
    
    def _load_task_group(self, tasks: Dict[str, Callable], max_workers: int = None):
        """Load a group of tasks concurrently with individual timing"""
        
        if max_workers is None:
            max_workers = self.max_workers
            
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # Submit tasks with start time
            future_to_key = {}
            task_start_times = {}
            
            for key, task_func in tasks.items():
                task_start_times[key] = time.time()
                future_to_key[executor.submit(task_func)] = key
            
            for future in as_completed(future_to_key):
                key = future_to_key[future]
                task_duration = time.time() - task_start_times[key]
                
                try:
                    result = future.result(timeout=60)  # 60 second timeout per query
                    self.results[key] = result
                    self.task_timings[key] = task_duration
                    print(f"✓ Completed {key} in {task_duration:.2f}s")
                except Exception as exc:
                    print(f"✗ Error loading {key} after {task_duration:.2f}s: {exc}")
                    self.results[key] = None
                    self.task_timings[key] = task_duration
                    self.failed_tasks.append(key)
    
    def _load_task_group_with_retry(self, tasks: Dict[str, Callable]):
        """Retry failed critical tasks with timing"""
        for attempt in range(self.max_retries):
            if not tasks:
                break
                
            print(f"Retry attempt {attempt + 1} for {list(tasks.keys())}")
            
            remaining_tasks = {}
            for key, task_func in tasks.items():
                start_time = time.time()
                try:
                    result = task_func()
                    duration = time.time() - start_time
                    self.results[key] = result
                    self.task_timings[f"{key}_retry_{attempt + 1}"] = duration
                    print(f"✓ Retry successful for {key} in {duration:.2f}s")
                except Exception as exc:
                    duration = time.time() - start_time
                    print(f"✗ Retry failed for {key} after {duration:.2f}s: {exc}")
                    self.task_timings[f"{key}_retry_{attempt + 1}_failed"] = duration
                    remaining_tasks[key] = task_func
            
            tasks = remaining_tasks
    
    def _print_timing_summary(self):
        """Print a summary of task timings"""
        if not self.task_timings:
            return
            
        print("\n" + "="*50)
        print("TASK TIMING SUMMARY")
        print("="*50)
        
        # Sort tasks by duration (longest first)
        sorted_timings = sorted(self.task_timings.items(), key=lambda x: x[1], reverse=True)
        
        total_time = sum(self.task_timings.values())
        
        for task_name, duration in sorted_timings:
            percentage = (duration / total_time) * 100 if total_time > 0 else 0
            status = "✓" if not any(task_name.startswith(failed) for failed in self.failed_tasks) else "✗"
            print(f"{status} {task_name:<50} {duration:>8.2f}s ({percentage:>5.1f}%)")
        
        print("-" * 70)
        print(f"{'Total execution time:':<50} {total_time:>8.2f}s")
        print("="*50)
    
    def get_task_timings(self):
        """Return the task timings dictionary for programmatic access"""
        return self.task_timings.copy()
    
    def _assign_to_config(self):
        """Assign loaded data to config object"""
        config.all_revenue_details = self.results.get('all_revenue_details')
        config.retail_flag_all = self.results.get('retail_flag_all')
        config.menu_master_df = self.results.get('menu_master_df')
        config.menu_service_type_df = self.results.get('menu_service_type_df')
        config.assigned_menu_models_df = self.results.get('assigned_menu_models_df')
        config.assigned_menu_opcodes_df = self.results.get('assigned_menu_opcodes_df')
        config.mpi_setup_df = self.results.get('mpi_setup_df')
        config.mpi_opcodes = self.results.get('mpi_opcodes')
        config.all_revenue_details_for_client_report_card_3_month = self.results.get('all_revenue_details_for_client_report_card_3_month')
