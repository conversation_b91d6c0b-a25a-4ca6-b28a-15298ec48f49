# FOPC UI Validation

This project validates the FOPC UI by comparing UI data with backend/calculated results and generates comprehensive reports. The project includes scripts for data extraction, transformation, comparison, and reporting.

---

## Omni AI Installation Requirements

Omni AI works with Python 3.12.0 or higher.  
**Please upgrade your Python version if it's older.**

### To install the latest Python version:

1. Download the latest version from the official site:  
   [https://www.python.org/downloads/](https://www.python.org/downloads/)
2. Run the installer and **check the box that says: "Add Python to PATH"** before installing.

---

## Poppler Installation (Required for PDF Processing)

Omni AI also requires Poppler for PDF processing.

### For Windows:

1. Download Poppler for Windows from:  
   [https://github.com/oschwartz10612/poppler-windows/releases/](https://github.com/oschwartz10612/poppler-windows/releases/)
2. Unzip the downloaded folder.
3. Add the `bin` directory from the unzipped folder to your System PATH:
   - Search for "Edit the system environment variables"
   - Click **Environment Variables**
   - Under **System variables**, select **Path** > click **Edit** > **New**
   - Paste the full path to the `bin` folder (e.g., `C:\poppler\Library\bin`)
   - Click **OK** to save and close all dialogs.

### For Ubuntu/Debian:

Open your terminal and run:
```sh
sudo apt update
sudo apt install poppler-utils
```

---

## Project Structure

```
lib/
├── pattern/
│   ├── run_all_tests.py
│   ├── config.py
│   ├── report_generator.py
│   └── sampackag/
│       ├── validate_kpi_scorecard.py
│       ├── validate_client_report_card.py
│       ├── validate_client_report_card_3_months.py
│       ├── compare_kpi_dashboard.py
│       ├── compare_client_report_card.py
│       └── compare_client_report_card_3_months.py
├── std/
│   └── universal/
│       └── extract_image_data.py
.env
requirements.txt
```

### Key Files

- **validate_kpi_scorecard.py**: Validates KPI scorecards.
- **validate_client_report_card.py**: Validates Client Report Card (1 month).
- **validate_client_report_card_3_months.py**: Validates Client Report Card (3 months).
- **compare_kpi_dashboard.py**: Compares KPI dashboard values and generates reports.
- **compare_client_report_card.py**: Compares Client Report Card (1 month) values and generates reports.
- **compare_client_report_card_3_months.py**: Compares Client Report Card (3 months) values and generates reports.
- **report_generator.py**: Combines individual HTML reports into a consolidated report.
- **config.py**: Holds runtime parameters and configuration.
- **.env**: Environment variables required for the scripts.

---

## Setup

### 1. Clone the Repository

```sh
git clone <repository_url>
cd <repository_directory>
```

### 2. Create and Activate a Virtual Environment

It is recommended to use a Python virtual environment to manage dependencies:

On **Windows**:
```sh
python -m venv venv
venv\Scripts\activate
```

On **macOS/Linux**:
```sh
python3 -m venv venv
source venv/bin/activate
```

### 3. Install Dependencies

```sh
pip install -r requirements.txt
```

### 4. Set Up Environment Variables

Create a `.env` file in the root directory with the following variables:

```env
# Site credentials
fopc_username=""
password=""

# Database credentials
db_user_name=""
db_password=""
host=""
port=""

# Gemini API details
GEMINI_API_KEY=""
GEMINI_MODEL=""

```

Set the `.env` variables to define credentials consistently across different outputs.

---

## Usage

### 1. Run the Validation Script

You can execute the main script from the command line with the required CLI arguments. Example:

```sh
python -m lib.pattern.run_all_tests \
  --store_id 254322619 \
  --store_name "Five Star Ford of North Richland Hills" \
  --start_date 2024-07-01 \
  --end_date 2024-09-30 \
  --fopc_month 2024-10 \
  --pre_fopc_month 2024-09 \
  --database_name sampackag \
  --working_days 73.8 \
  --advisor all \
  --technician all \
  --last_month 2024-10 \
  --site_url "https://sampackag.fixedops.cc/" \
  --role Admin
```

**Required CLI Arguments:**

- `--store_id`: Store ID
- `--store_name`: Store Name
- `--start_date`: Start date (YYYY-MM-DD)
- `--end_date`: End date (YYYY-MM-DD)
- `--fopc_month`: Current FOPC month (YYYY-MM)
- `--pre_fopc_month`: Previous FOPC month (YYYY-MM)
- `--database_name`: Database name (e.g., sampackag)
- `--working_days`: Number of working days in the selected date range (e.g., 46.74)
- `--advisor`: Advisor name or 'all'
- `--technician`: Technician name or 'all'
- `--last_month`: Last month for client report card (YYYY-MM)
- `--site_url`: Base URL of the site (e.g., https://sampackag.fixedops.cc/)
- `--role`: Name of the role

---

## Output

- **Consolidated Report:**  
  The final consolidated HTML report is saved in the folder `Final_Consolidated_Report-tenant_name` as `consolidated_report.html`. This report includes the comparison results for the KPI Dashboard, Client Report Card (1 Month), and Client Report Card (3 Months).

- **Intermediate Results:**  
  Individual results are saved in the `Individual_Reports-tenant_name` folder (the default folder). For example, for the KPI dashboard, the following files are generated:
  - `comparison_results_kpi_dashboard.csv`: CSV file with comparison results.
  - `comparison_results_kpi_dashboard_highlighted.xlsx`: Excel file with highlighted mismatches.
  - `kpi_dashboard.json`: JSON file with comparison results.
  - `kpi_dashboard.html`: HTML file with comparison results.

---

## Troubleshooting

- Ensure all required dependencies are installed using `pip install -r requirements.txt`.
- Verify that the `.env` credentials are correct.
- For Playwright/browser automation, ensure all required browsers are installed (see Playwright documentation).
- For PDF processing, ensure Poppler is installed and added to your system PATH as described above.

---