"""
Validation script for Client Report Card (3 months).
Automates UI, extracts data, compares with DB, and generates validation reports.
"""

import os
import json
import logging
from decimal import Decimal, ROUND_HALF_UP
from datetime import datetime
from typing import Any, Dict
import pandas as pd
from dotenv import load_dotenv
from playwright.sync_api import sync_playwright
from dateutil.relativedelta import relativedelta

from lib.std.universal.extract_image_data import extract_image_data_split
from lib.pattern.sampackag.compare_client_report_card_3_months import compare_client_report_card_3_months
from lib.pattern.config import config
from lib.std.universal.utils import create_folder_file_path

# Configure logging
logging.basicConfig(level=logging.INFO, format="%(levelname)s: %(message)s")

load_dotenv()


CUSTOM_SYSTEM_PROMPT = ( "Analyze this document and provide the contents in JSON format. The labels should be correctly mapped as in the image "
    '''
    The output should be in the following format
    {
  "Monthly FOPC": "value",
  "Monthly DMS": "value",
  "Total": "value",
  "ROI": "value%",
  "Time Period": "value",
  "Total Pts & Lbr GP Change": "value",
  "Repair ELR Change": "value",
  "ROI_Annualized": "value%",
  "Time Period_Annualized": "value",
  "Total Pts & Lbr GP Change_Annualized": "value",
  "Repair ELR Change_Annualized": "value",
  "KPIs": {
    "RO Count": {
      "3 MTH Avg (Baseline)": "value",
      "Last Month": "value",
      "Variance": "value",
      "Prior Annual Pace": "value",
      "Annual Pace": "value",
      "Variance Annualized": "value"
    },
    "Hours Sold": {
      "3 MTH Avg (Baseline)": "value",
      "Last Month": "value",
      "Variance": "value",
      "Prior Annual Pace": "value",
      "Annual Pace": "value",
      "Variance Annualized": "value"
    },
    "Cust. Pay Hrs Per RO": {
      "3 MTH Avg (Baseline)": "value",
      "Last Month": "value",
      "Variance": "value",
      "Prior Annual Pace": "value",
      "Annual Pace": "value",
      "Variance Annualized": "value"
    },
    "Total Shop ELR": {
      "3 MTH Avg (Baseline)": "value",
      "Last Month": "value",
      "Variance": "value",
      "Prior Annual Pace": "value",
      "Annual Pace": "value",
      "Variance Annualized": "value"
    },
    "Total Labor GP%": {
      "3 MTH Avg (Baseline)": "value%",
      "Last Month": "value%",
      "Variance": "value%",
      "Prior Annual Pace": "value%",
      "Annual Pace": "value%",
      "Variance Annualized": "value%"
    },
    "Total Parts GP%": {
      "3 MTH Avg (Baseline)": "value%",
      "Last Month": "value%",
      "Variance": "value%",
      "Prior Annual Pace": "value%",
      "Annual Pace": "value%",
      "Variance Annualized": "value%"
    },
    "Total Labor Sold": {
     "3 MTH Avg (Baseline)": "value",
      "Last Month": "value",
      "Variance": "value",
      "Prior Annual Pace": "value",
      "Annual Pace": "value",
      "Variance Annualized": "value"
    },
    "Total Labor GP": {
      "3 MTH Avg (Baseline)": "value",
      "Last Month": "value",
      "Variance": "value",
      "Prior Annual Pace": "value",
      "Annual Pace": "value",
      "Variance Annualized": "value"
    },
    "Total Parts Sale": {
      "3 MTH Avg (Baseline)": "value",
      "Last Month": "value",
      "Variance": "value",
      "Prior Annual Pace": "value",
      "Annual Pace": "value",
      "Variance Annualized": "value"
    },
    "Total Parts GP": {
      "3 MTH Avg (Baseline)": "value",
      "Last Month": "value",
      "Variance": "value",
      "Prior Annual Pace": "value",
      "Annual Pace": "value",
      "Variance Annualized": "value"
    },
    "Total Lbr & Pts Sales": {
      "3 MTH Avg (Baseline)": "value",
      "Last Month": "value",
      "Variance": "value",
      "Prior Annual Pace": "value",
      "Annual Pace": "value",
      "Variance Annualized": "value"
    },
    "Total Lbr & Pts GP": {
      "3 MTH Avg (Baseline)": "value",
      "Last Month": "value",
      "Variance": "value",
      "Prior Annual Pace": "value",
      "Annual Pace": "value",
      "Variance Annualized": "value"
    }
  },
"COMPETITIVE": {
    "Hours Sold": {
      "3 MTH Avg (Baseline)": "value",
      "Last Month": "value",
      "Variance": "value",
      "Prior Annual Pace": "value",
      "Annual Pace": "value",
      "Variance Annualized": "value"
    },
    "ELR": {
     "3 MTH Avg (Baseline)": "value",
      "Last Month": "value",
      "Variance": "value",
      "Prior Annual Pace": "value",
      "Annual Pace": "value",
      "Variance Annualized": "value"
    },
    "% of Total Shop Hours": {
      "3 MTH Avg (Baseline)": "value%",
      "Last Month": "value%",
      "Variance": "value%",
      "Prior Annual Pace": "value%",
      "Annual Pace": "value%",
      "Variance Annualized": "value%"
    },
    "Total Labor Sold": {
      "3 MTH Avg (Baseline)": "value",
      "Last Month": "value",
      "Variance": "value",
      "Prior Annual Pace": "value",
      "Annual Pace": "value",
      "Variance Annualized": "value"
    },
    "Total Labor GP": {
      "3 MTH Avg (Baseline)": "value",
      "Last Month": "value",
      "Variance": "value",
      "Prior Annual Pace": "value",
      "Annual Pace": "value",
      "Variance Annualized": "value"
    },
    "Total Labor GP%": {
      "3 MTH Avg (Baseline)": "value%",
      "Last Month": "value%",
      "Variance": "value%",
      "Prior Annual Pace": "value%",
      "Annual Pace": "value%",
      "Variance Annualized": "value%"
    },
    "Total Parts Sale": {
      "3 MTH Avg (Baseline)": "value",
      "Last Month": "value",
      "Variance": "value",
      "Prior Annual Pace": "value",
      "Annual Pace": "value",
      "Variance Annualized": "value"
    },
    "Total Parts GP": {
      "3 MTH Avg (Baseline)": "value",
      "Last Month": "value",
      "Variance": "value",
      "Prior Annual Pace": "value",
      "Annual Pace": "value",
      "Variance Annualized": "value"
    },
    "Total Parts GP%": {
      "3 MTH Avg (Baseline)": "value%",
      "Last Month": "value%",
      "Variance": "value%",
      "Prior Annual Pace": "value%",
      "Annual Pace": "value%",
      "Variance Annualized": "value%"
    },
     "Total Lbr & Pts Sales": {
      "3 MTH Avg (Baseline)": "value",
      "Last Month": "value",
      "Variance": "value",
      "Prior Annual Pace": "value",
      "Annual Pace": "value",
      "Variance Annualized": "value"
    },
    "Lbr & Pts GP": {
      "3 MTH Avg (Baseline)": "value",
      "Last Month": "value",
      "Variance": "value",
      "Prior Annual Pace": "value",
      "Annual Pace": "value",
      "Variance Annualized": "value"
    }
  },
  "MAINTENANCE": {
    "Hours Sold": {
      "3 MTH Avg (Baseline)": "value",
      "Last Month": "value",
      "Variance": "value",
      "Prior Annual Pace": "value",
      "Annual Pace": "value",
      "Variance Annualized": "value"
    },
    "ELR": {
     "3 MTH Avg (Baseline)": "value",
      "Last Month": "value",
      "Variance": "value",
      "Prior Annual Pace": "value",
      "Annual Pace": "value",
      "Variance Annualized": "value"
    },
    "% of Total Shop Hours": {
      "3 MTH Avg (Baseline)": "value%",
      "Last Month": "value%",
      "Variance": "value%",
      "Prior Annual Pace": "value%",
      "Annual Pace": "value%",
      "Variance Annualized": "value%"
    },
    "Total Labor Sold": {
      "3 MTH Avg (Baseline)": "value",
      "Last Month": "value",
      "Variance": "value",
      "Prior Annual Pace": "value",
      "Annual Pace": "value",
      "Variance Annualized": "value"
    },
    "Total Labor GP": {
      "3 MTH Avg (Baseline)": "value",
      "Last Month": "value",
      "Variance": "value",
      "Prior Annual Pace": "value",
      "Annual Pace": "value",
      "Variance Annualized": "value"
    },
    "Total Labor GP%": {
      "3 MTH Avg (Baseline)": "value%",
      "Last Month": "value%",
      "Variance": "value%",
      "Prior Annual Pace": "value%",
      "Annual Pace": "value%",
      "Variance Annualized": "value%"
    },
    "Total Parts Sale": {
      "3 MTH Avg (Baseline)": "value",
      "Last Month": "value",
      "Variance": "value",
      "Prior Annual Pace": "value",
      "Annual Pace": "value",
      "Variance Annualized": "value"
    },
    "Total Parts GP": {
      "3 MTH Avg (Baseline)": "value",
      "Last Month": "value",
      "Variance": "value",
      "Prior Annual Pace": "value",
      "Annual Pace": "value",
      "Variance Annualized": "value"
    },
    "Total Parts GP%": {
      "3 MTH Avg (Baseline)": "value%",
      "Last Month": "value%",
      "Variance": "value%",
      "Prior Annual Pace": "value%",
      "Annual Pace": "value%",
      "Variance Annualized": "value%"
    },
     "Total Lbr & Pts Sales": {
      "3 MTH Avg (Baseline)": "value",
      "Last Month": "value",
      "Variance": "value",
      "Prior Annual Pace": "value",
      "Annual Pace": "value",
      "Variance Annualized": "value"
    },
    "Lbr & Pts GP": {
      "3 MTH Avg (Baseline)": "value",
      "Last Month": "value",
      "Variance": "value",
      "Prior Annual Pace": "value",
      "Annual Pace": "value",
      "Variance Annualized": "value"
    }
  },
  "REPAIR": {
    "Hours Sold": {
      "3 MTH Avg (Baseline)": "value",
      "Last Month": "value",
      "Variance": "value",
      "Prior Annual Pace": "value",
      "Annual Pace": "value",
      "Variance Annualized": "value"
    },
    "ELR": {
     "3 MTH Avg (Baseline)": "value",
      "Last Month": "value",
      "Variance": "value",
      "Prior Annual Pace": "value",
      "Annual Pace": "value",
      "Variance Annualized": "value"
    },
    "% of Total Shop Hours": {
      "3 MTH Avg (Baseline)": "value%",
      "Last Month": "value%",
      "Variance": "value%",
      "Prior Annual Pace": "value%",
      "Annual Pace": "value%",
      "Variance Annualized": "value%"
    },
    "Total Labor Sold": {
      "3 MTH Avg (Baseline)": "value",
      "Last Month": "value",
      "Variance": "value",
      "Prior Annual Pace": "value",
      "Annual Pace": "value",
      "Variance Annualized": "value"
    },
    "Total Labor GP": {
      "3 MTH Avg (Baseline)": "value",
      "Last Month": "value",
      "Variance": "value",
      "Prior Annual Pace": "value",
      "Annual Pace": "value",
      "Variance Annualized": "value"
    },
    "Total Labor GP%": {
      "3 MTH Avg (Baseline)": "value%",
      "Last Month": "value%",
      "Variance": "value%",
      "Prior Annual Pace": "value%",
      "Annual Pace": "value%",
      "Variance Annualized": "value%"
    },
    "Total Parts Sale": {
      "3 MTH Avg (Baseline)": "value",
      "Last Month": "value",
      "Variance": "value",
      "Prior Annual Pace": "value",
      "Annual Pace": "value",
      "Variance Annualized": "value"
    },
    "Total Parts GP": {
      "3 MTH Avg (Baseline)": "value",
      "Last Month": "value",
      "Variance": "value",
      "Prior Annual Pace": "value",
      "Annual Pace": "value",
      "Variance Annualized": "value"
    },
    "Total Parts GP%": {
      "3 MTH Avg (Baseline)": "value%",
      "Last Month": "value%",
      "Variance": "value%",
      "Prior Annual Pace": "value%",
      "Annual Pace": "value%",
      "Variance Annualized": "value%"
    },
     "Total Lbr & Pts Sales": {
      "3 MTH Avg (Baseline)": "value",
      "Last Month": "value",
      "Variance": "value",
      "Prior Annual Pace": "value",
      "Annual Pace": "value",
      "Variance Annualized": "value"
    },
    "Lbr & Pts GP": {
      "3 MTH Avg (Baseline)": "value",
      "Last Month": "value",
      "Variance": "value",
      "Prior Annual Pace": "value",
      "Annual Pace": "value",
      "Variance Annualized": "value"
    }
  }
}
    '''
)


def round_off(n: float, decimals: int = 0) -> float:
    """Round a number to a given number of decimal places using ROUND_HALF_UP."""
    multiplier = Decimal(10) ** decimals
    if isinstance(n, float):
        n = Decimal(str(n))
    return float((n * multiplier).quantize(Decimal("1"), rounding=ROUND_HALF_UP) / multiplier)

def is_empty(value: Any) -> bool:
    """Check if a value is considered empty for merging."""
    return value in [None, "", 'value', 'value%']

def merge_missing_values(base: Any, update: Any) -> Any:
    """Recursively merge missing values from update into base."""
    if isinstance(base, dict) and isinstance(update, dict):
        for key in update:
            if key in base:
                base[key] = merge_missing_values(base[key], update[key])
            else:
                base[key] = update[key]
    elif isinstance(base, list) and isinstance(update, list):
        return [merge_missing_values(b, u) for b, u in zip(base, update)]
    else:
        return update if is_empty(base) else base
    return base

def automate_site() -> None:
    """
    Automate the UI to capture Client Report Card (3 months) screenshots.
    """
    # Get base URL from .env
    base_url = config.site_url
    path = "ThreeMonthReport"
    site_url = f"{base_url.rstrip('/')}/{path}"
    username = os.getenv("fopc_username")
    password = os.getenv("password")
    result_folder = create_folder_file_path(base_folder_name="Omni_Results",
                                tenant_name=config.database_name)
    store_name = config.store_name

    start_date = datetime.strptime(config.start_date, "%Y-%m-%d")
    end_date = datetime.strptime(config.end_date, "%Y-%m-%d")

    # Format: "MM/DD/YY - MM/DD/YY" (start to end)
    date_range = f"{end_date.strftime('%m/%d/%y')} - {start_date.strftime('%m/%d/%y')}"

    last_month_date = datetime.strptime(config.last_month, "%Y-%m")
    month_year = last_month_date.strftime("%b-%y")

    with sync_playwright() as p:
        browser = p.chromium.launch(headless=False, args=["--start-fullscreen"])
        context = browser.new_context(viewport={"width": 1920, "height": 1080}, device_scale_factor=1)
        page = context.new_page()
        #   Step 1: home
        page.goto(site_url)
        page.click("button#login", force=True)
        # Step 2: Login
        page.fill("input[name='username']", username)
        page.fill("input[name='password']", password)
        page.click("input#kc-login")
        # page.wait_for_url("**/ThreeMonthReport", timeout=15000)
        # After login is completed and page is ready
        page.wait_for_selector("#store-select", timeout=10000)
        page.click("#store-select")
        # Wait for dropdown to appear and select the option
        page.get_by_role("option", name=store_name).click()
        # Step 2: Click "View Dashboard"
        page.wait_for_selector("button:has-text('View Dashboard')", timeout=10000)
        page.click("button#login")
        page.goto(site_url)
        page.click("input#picker")
        page.fill("input[name='picker']", date_range)
        page.click("#mui-component-select-month-1")

        # Select the desired value (ensure this text matches exactly)
        page.get_by_role("option", name=month_year).click()

        page.wait_for_load_state("networkidle")
        page.get_by_label("Competitive").click()
        page.get_by_label("Maintenance").click()
        page.get_by_label("Repair").click()
        page.wait_for_timeout(5000)

        # Hide sticky header if needed (example selector, update if needed)
        page.evaluate("""
                    const header = document.querySelector('.sticky-header-class');
                    if(header) header.style.display = 'none';
                """)

        # Make table header sticky and ensure visibility
        page.evaluate("""
            const thead = document.querySelector('table.client-report-second.datatable thead');
            if (thead) {
                thead.style.position = 'sticky';
                thead.style.top = '0px';
                thead.style.backgroundColor = '#fff';
                thead.style.zIndex = '9999';
            }

            // Ensure parent containers don't block overflow
            let parent = thead?.parentElement;
            while (parent) {
                parent.style.overflow = 'visible';
                parent = parent.parentElement;
            }
        """)

        total_height = page.evaluate("document.body.scrollHeight")
        viewport_height = page.evaluate("window.innerHeight")
        overlap = 100  # pixels of overlap to avoid missing content

        scroll_step = viewport_height - overlap
        num_screenshots = (total_height + scroll_step - 1) // scroll_step  # ceiling division

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        for i in range(num_screenshots):
            scroll_y = i * scroll_step
            if scroll_y + viewport_height > total_height:
                scroll_y = total_height - viewport_height  # scroll last time to bottom
            page.evaluate(f"window.scrollTo(0, {scroll_y})")
            page.wait_for_timeout(1000)

            # Trigger scroll event in case sticky headers react to it
            page.evaluate("window.dispatchEvent(new Event('scroll'))")
            page.wait_for_timeout(500)
            # Make sure the output directory exists
            os.makedirs(result_folder, exist_ok=True)
            filename = os.path.join(result_folder, f"client_report_card_3_months{i + 1}.jpg")
            page.screenshot(path=filename, full_page=False)
            print(f"Saved: {filename}")

        browser.close()

automate_site()

def run_validation():
    # Get the result folder path from the .env
    result_folder = create_folder_file_path(base_folder_name="Omni_Results",
                                tenant_name=config.database_name)

    if result_folder:
        # Create folder if it doesn't exist
        if not os.path.exists(result_folder):
            os.makedirs(result_folder)
            print(f"Created folder: {result_folder}")
        else:
            print(f"Folder already exists: {result_folder}")
    else:
        print("RESULT_FOLDER not found in .env file")

    #Get the required environment variables
    storeid = config.store_id
    realm = config.database_name
    dms_fees = Decimal(os.environ.get('dms_fees', "0"))
    fopc_fees = Decimal(os.environ.get('fopc_fees',"0"))

    s_date_env = config.start_date
    e_date_env = config.end_date

    # Convert to pandas Timestamp (ensures proper handling of date formats)
    s_date = pd.Timestamp(s_date_env)
    e_date = pd.Timestamp(e_date_env)

    # Calculate required date range
    required_date_start = pd.date_range(start=s_date, end=e_date, freq='MS').strftime('%Y-%m').tolist()
    # Step 2: Generate 3-month range from the start of that month
    start_dt = datetime.strptime(required_date_start[0], "%Y-%m")
    required_date_range = [(start_dt + relativedelta(months=i)).strftime("%Y-%m") for i in range(3)]
    # calculate date ranges
    last_month = config.last_month
    last_month_date = datetime.strptime(last_month, "%Y-%m")
    date_ranges = [(last_month_date - relativedelta(months=i)).strftime("%Y-%m") for i in range(3)]

    # Fetching data from DB
    all_revenue_details_df = config.all_revenue_details_for_client_report_card_3_month
    retail_flag_all = config.retail_flag_all
    retail_flag = set(retail_flag_all['source_paytype'])

    # Initializing new data frame to filter only required advisor and technician
    filtered_df = all_revenue_details_df[
        (all_revenue_details_df['department'] == 'Service') &
        (all_revenue_details_df['opcategory'] != 'N/A') &
        (all_revenue_details_df['opcategory'].isin(['REPAIR','COMPETITIVE','MAINTENANCE'])) &
        (all_revenue_details_df['hide_ro'] != True)
    & (all_revenue_details_df['store_id'].astype(str).str.strip() == storeid)
        ]

    merged_df = filtered_df.merge(
        retail_flag_all,
        left_on=['paytypegroup', 'store_id'],
        right_on=['source_paytype', 'store_id'],
        how='left'
    )

    merged_df = merged_df.copy()  # Create a deep copy of filtered_df to avoid the warning
    merged_df['unique_ro_number'] = merged_df['ronumber'].astype(str) + '_' + merged_df['closeddate'].astype(str)

    # Define customer and warranty pay types dynamically
    if 'C' in retail_flag and not 'E' in retail_flag and not 'M' in retail_flag:
        customer_pay_types = {'C'}
        warranty_pay_types = {'W', 'F', 'M', 'E'}
    elif 'C' in retail_flag and not 'E' in retail_flag and 'M' in retail_flag:
        customer_pay_types = {'C', 'M'}
        warranty_pay_types = {'W', 'F', 'E'}
    elif 'C' in retail_flag and 'E' in retail_flag and not 'M' in retail_flag:
        customer_pay_types = {'C', 'E'}
        warranty_pay_types = {'W', 'F', 'M'}
    elif 'C' in retail_flag and 'E' in retail_flag and 'M' in retail_flag:
        customer_pay_types = {'C', 'E', 'M'}
        warranty_pay_types = {'W', 'F'}

    total_CP_revenue_details_df = merged_df[merged_df['paytypegroup'].isin(customer_pay_types)]

    total_CP_revenue_details_df = total_CP_revenue_details_df.copy()

    # Use .loc to set the values
    total_CP_revenue_details_df.loc[:, 'lbrsale'] = pd.to_numeric(total_CP_revenue_details_df['lbrsale'].fillna(0), errors='coerce')
    total_CP_revenue_details_df.loc[:, 'lbrsoldhours'] = pd.to_numeric(total_CP_revenue_details_df['lbrsoldhours'].fillna(0), errors='coerce')
    total_CP_revenue_details_df.loc[:, 'prtextendedsale'] = pd.to_numeric(total_CP_revenue_details_df['prtextendedsale'].fillna(0), errors='coerce')
    total_CP_revenue_details_df.loc[:, 'prtextendedcost'] = pd.to_numeric(total_CP_revenue_details_df['prtextendedcost'].fillna(0), errors='coerce')

    total_CP_revenue_details_df = total_CP_revenue_details_df[
    ~((total_CP_revenue_details_df['lbrsale'].fillna(0) == 0) &
        (total_CP_revenue_details_df['lbrsoldhours'].fillna(0) == 0) &
        (total_CP_revenue_details_df['prtextendedsale'].fillna(0) == 0) &
        (total_CP_revenue_details_df['prtextendedcost'].fillna(0) == 0))
    ]


    total_CP_revenue_L_month = total_CP_revenue_details_df[total_CP_revenue_details_df['month_year'] == last_month]
    total_CP_revenue_L_3_month = total_CP_revenue_details_df[total_CP_revenue_details_df['month_year'].isin(date_ranges)]
    total_CP_revenue_3_month = total_CP_revenue_details_df[total_CP_revenue_details_df['month_year'].isin(required_date_range)]

    Client_Report_Card = []
    # RO Count calculation
    RO_count_3_month_avg_value = 0
    RO_count_3_month_avg = 0
    RO_count_prior_annual_pace = 0
    Sold_hours_3_month_avg_value = 0
    Sold_hours_3_month_avg = 0
    Sold_hours_prior_annual_pace = 0

    if not total_CP_revenue_3_month.empty:
        #RO Count calculation
        RO_count_3_month = total_CP_revenue_3_month['unique_ro_number'].nunique()
        RO_count_3_month_avg_value = RO_count_3_month / 3
        RO_count_3_month_avg = round_off(RO_count_3_month_avg_value)
        RO_count_prior_annual_pace_value = RO_count_3_month_avg_value * 12
        RO_count_prior_annual_pace = round_off(RO_count_prior_annual_pace_value)
        # Sold Hours Calculations
        Sold_hours_3_month = total_CP_revenue_3_month[(total_CP_revenue_3_month['opcategory'] != 'N/A') &
                                    (total_CP_revenue_3_month['opcategory'] != 'SHOP SUPPLIES')]['lbrsoldhours'].sum()
        # Sold_hous_3_month = total_CP_revenue_3_month['lbrsoldhours'].sum()
        Sold_hours_3_month_avg_value = Sold_hours_3_month / 3
        Sold_hours_3_month_avg = round_off(Sold_hours_3_month_avg_value)
        Sold_hours_prior_annual_pace_value = Sold_hours_3_month_avg_value * 12
        Sold_hours_prior_annual_pace = round_off(Sold_hours_prior_annual_pace_value)
        #Hours Per RO calculations

        if RO_count_3_month != 0:
            Hours_per_RO_3_month_avg_value = Sold_hours_3_month / RO_count_3_month
            Hours_per_RO_3_month_avg = round_off(Hours_per_RO_3_month_avg_value)


        if RO_count_prior_annual_pace_value != 0:
            Hours_per_RO_prior_annual_pace_value = Sold_hours_prior_annual_pace_value / RO_count_prior_annual_pace_value
            Hours_per_RO_prior_annual_pace = round_off(Hours_per_RO_prior_annual_pace_value)
        # ELR calculation
        Labor_sale_value_3_month = float(total_CP_revenue_3_month['lbrsale'].sum())

        if Sold_hours_3_month != 0:
            Total_ELR_3_month_value = Labor_sale_value_3_month / Sold_hours_3_month
            Total_ELR_3_month = round_off(Total_ELR_3_month_value, 2)
        Total_ELR_prior_annual_pace = Total_ELR_3_month
        total_CP_revenue_3_month_REP = total_CP_revenue_3_month[total_CP_revenue_3_month['opcategory'] == 'REPAIR']

        if not total_CP_revenue_3_month_REP.empty:
            total_CP_revenue_3_month_REP_sale = total_CP_revenue_3_month_REP['lbrsale'].sum()
            total_CP_revenue_3_month_REP_hours = total_CP_revenue_3_month_REP['lbrsoldhours'].sum()


        if total_CP_revenue_3_month_REP_hours != 0:
            Repair_ELR_3_month_avg_value = total_CP_revenue_3_month_REP_sale / total_CP_revenue_3_month_REP_hours
            Repair_ELR_3_month_avg = round_off(Repair_ELR_3_month_avg_value, 2)
            Repair_ELR_prior_annual_pace_value = Repair_ELR_3_month_avg_value
            Repair_ELR_prior_annual_pace = Repair_ELR_3_month_avg
        #Labor Gross Profit %
        Labor_sale_value_3_month = float(total_CP_revenue_3_month['lbrsale'].sum())
        Labor_cost_value_3_month = float(total_CP_revenue_3_month['lbrcost'].sum())

        if Labor_sale_value_3_month != 0:
            Labor_GP_perc_3_month_avg_value = ((Labor_sale_value_3_month - Labor_cost_value_3_month) / Labor_sale_value_3_month) * 100
            Labor_GP_perc_3_month_avg = round_off(Labor_GP_perc_3_month_avg_value)
            Labor_GP_perc_prior_annual_pace = Labor_GP_perc_3_month_avg
        #Parts Gross Profit %
        Parts_sale_value_3_month = float(total_CP_revenue_3_month['prtextendedsale'].sum())
        Parts_cost_value_3_month = float(total_CP_revenue_3_month['prtextendedcost'].sum())


        if Parts_sale_value_3_month != 0:
            Parts_GP_perc_3_month_avg_value = ((Parts_sale_value_3_month - Parts_cost_value_3_month) / Parts_sale_value_3_month) * 100
            Parts_GP_perc_3_month_avg = round_off(Parts_GP_perc_3_month_avg_value)
            Parts_GP_perc_prior_annual_pace_value = Parts_GP_perc_3_month_avg_value
            Parts_GP_perc_prior_annual_pace = Parts_GP_perc_3_month_avg

        #Labor Sale
        Labor_sale_3_month_avg_value = Labor_sale_value_3_month / 3
        Labor_sale_3_month_avg = round_off(Labor_sale_3_month_avg_value)
        Labor_sale_prior_annual_pace_value = Labor_sale_3_month_avg_value * 12
        Labor_sale_prior_annual_pace = round_off(Labor_sale_prior_annual_pace_value)
        #Labor GP
        Labor_GP_3_month_avg_value = (Labor_sale_value_3_month - Labor_cost_value_3_month) / 3
        Labor_GP_3_month_avg = round_off(Labor_GP_3_month_avg_value)
        Labor_GP_prior_annual_pace_value = Labor_GP_3_month_avg_value * 12
        Labor_GP_prior_annual_pace = round_off(Labor_GP_prior_annual_pace_value)
        #Parts Sale
        Parts_sale_3_month_avg_value = Parts_sale_value_3_month / 3
        Parts_sale_3_month_avg = round_off(Parts_sale_3_month_avg_value)
        Parts_sale_prior_annual_pace_value = Parts_sale_3_month_avg_value * 12
        Parts_sale_prior_annual_pace = round_off(Parts_sale_prior_annual_pace_value)
        #Labor GP
        Parts_GP_3_month_avg_value = (Parts_sale_value_3_month - Parts_cost_value_3_month) / 3
        Parts_GP_3_month_avg = round_off(Parts_GP_3_month_avg_value)
        Parts_GP_prior_annual_pace_value = Parts_GP_3_month_avg_value * 12
        Parts_GP_prior_annual_pace = round_off(Parts_GP_prior_annual_pace_value)
        #Total Labor & Parts Sale
        Labor_Parts_sale_3_month_avg_value = (Labor_sale_value_3_month + Parts_sale_value_3_month) / 3
        Labor_Parts_sale_3_month_avg = round_off(Labor_Parts_sale_3_month_avg_value)
        Labor_Parts_sale_prior_annual_pace_value = Labor_Parts_sale_3_month_avg_value * 12
        Labor_Parts_sale_prior_annual_pace = round_off(Labor_Parts_sale_prior_annual_pace_value)
        #Total Labor & Parts GP
        Labor_Parts_GP_3_month_avg_value = ((Labor_sale_value_3_month + Parts_sale_value_3_month) - (Labor_cost_value_3_month + Parts_cost_value_3_month)) / 3
        Labor_Parts_GP_3_month_avg = round_off(Labor_Parts_GP_3_month_avg_value)
        Labor_Parts_GP_prior_annual_pace_value = Labor_Parts_GP_3_month_avg_value * 12
        Labor_Parts_GP_prior_annual_pace = round_off(Labor_Parts_GP_prior_annual_pace_value)



    RO_count_L_month = 0
    Sold_hours_L_month_value = 0
    Sold_hours_L_month = 0
    Hours_per_RO_L_month_value = 0
    Hours_per_RO_L_month = 0
    Total_ELR_L_month_value = 0
    Total_ELR_L_month = 0
    Repair_ELR_L_month_value = 0
    Repair_ELR_L_month = 0
    total_CP_revenue_L_month_REP_sale = 0
    total_CP_revenue_L_month_REP_hours = 0
    Labor_GP_perc_L_month_value = 0
    Labor_GP_perc_L_month = 0

    if not total_CP_revenue_L_month.empty:
        # RO Count calculation
        RO_count_L_month = total_CP_revenue_L_month['unique_ro_number'].nunique()
        # Sold Hours Calculations
        # [
                                    # (total_CP_revenue_L_month['opcategory'] != 'N/A') &
                                    # (total_CP_revenue_L_month['opcategory'] != 'SHOP SUPPLIES')
                                    # ]
        Sold_hours_L_month_value = total_CP_revenue_L_month['lbrsoldhours'].sum()
        Sold_hours_L_month = round_off(Sold_hours_L_month_value)
        #Hours per RO calculation

        if RO_count_L_month != 0:
            Hours_per_RO_L_month_value = Sold_hours_L_month_value / RO_count_L_month
            Hours_per_RO_L_month = round_off(Hours_per_RO_L_month_value)
        # ELR calculation
        Labor_sale_value_L_month = float(total_CP_revenue_L_month['lbrsale'].sum())

        if Sold_hours_L_month != 0:
            Total_ELR_L_month_value = Labor_sale_value_L_month / Sold_hours_L_month_value
            Total_ELR_L_month = round_off(Total_ELR_L_month_value, 2)
        total_CP_revenue_L_month_REP = total_CP_revenue_L_month[total_CP_revenue_L_month['opcategory'] == 'REPAIR']

        if not total_CP_revenue_L_month_REP.empty:
            total_CP_revenue_L_month_REP_sale = total_CP_revenue_L_month_REP['lbrsale'].sum()
            total_CP_revenue_L_month_REP_hours = total_CP_revenue_L_month_REP['lbrsoldhours'].sum()


        if total_CP_revenue_L_month_REP_hours != 0:
            Repair_ELR_L_month_value = total_CP_revenue_L_month_REP_sale / total_CP_revenue_L_month_REP_hours
            Repair_ELR_L_month = round_off(Repair_ELR_L_month_value, 2)
        #Labor Gross Profit %
        Labor_sale_value_L_month = float(total_CP_revenue_L_month['lbrsale'].sum())
        Labor_cost_value_L_month = float(total_CP_revenue_L_month['lbrcost'].sum())

        if Labor_sale_value_L_month != 0:
            Labor_GP_perc_L_month_value = ((Labor_sale_value_L_month - Labor_cost_value_L_month) / Labor_sale_value_L_month) * 100
            Labor_GP_perc_L_month = round_off(Labor_GP_perc_L_month_value)
            Labor_GP_perc_prior_annual_pace_value = Labor_GP_perc_L_month_value
        #Parts Gross Profit %
        Parts_sale_value_L_month = float(total_CP_revenue_L_month['prtextendedsale'].sum())
        Parts_cost_value_L_month = float(total_CP_revenue_L_month['prtextendedcost'].sum())

        if Parts_sale_value_L_month != 0:
            Parts_GP_perc_L_month_value = ((Parts_sale_value_L_month - Parts_cost_value_L_month) / Parts_sale_value_L_month) * 100
            Parts_GP_perc_L_month = round_off(Parts_GP_perc_L_month_value)
            #Parts_GP_perc_prior_annual_pace_value = Parts_GP_perc_L_month_value
        #Labor Sale
        Labor_sale_L_month_value = Labor_sale_value_L_month
        Labor_sale_L_month = round_off(Labor_sale_L_month_value)
        #Labor GP
        Labor_GP_L_month_value = Labor_sale_value_L_month - Labor_cost_value_L_month
        Labor_GP_L_month = round_off(Labor_GP_L_month_value)
        #Parts Sale
        Parts_sale_L_month_value = Parts_sale_value_L_month
        Parts_sale_L_month = round_off(Parts_sale_L_month_value)
        #Labor GP
        Parts_GP_L_month_value = Parts_sale_value_L_month - Parts_cost_value_L_month
        Parts_GP_L_month = round_off(Parts_GP_L_month_value)
        #Total Labor & Parts Sale
        Labor_Parts_sale_L_month_value = Labor_sale_value_L_month + Parts_sale_value_L_month
        Labor_Parts_sale_L_month = round_off(Labor_Parts_sale_L_month_value)
        #Total Labor & Parts GP
        Labor_Parts_GP_L_month_value = (Labor_sale_value_L_month + Parts_sale_value_L_month) - (Labor_cost_value_L_month + Parts_cost_value_L_month)
        Labor_Parts_GP_L_month = round_off(Labor_Parts_GP_L_month_value)


    RO_count_current_annual_pace = 0
    RO_count_L_3_month_avg_value = 0
    RO_count_L_3_month_avg = 0
    Sold_hours_L_3_month_avg_value = 0
    Sold_hours_L_3_month_avg = 0
    RO_count_current_annual_pace_value = 0
    Sold_hours_current_annual_pace = 0

    # Labor_Parts_sale_L_month=0
    if not total_CP_revenue_L_3_month.empty:
        #RO Count calculation
        RO_count_L_3_month = total_CP_revenue_L_3_month['unique_ro_number'].nunique()
        RO_count_L_3_month_avg_value = RO_count_L_3_month / 3
        RO_count_L_3_month_avg = round_off(RO_count_L_3_month_avg_value)
        RO_count_current_annual_pace_value = RO_count_L_3_month_avg_value * 12
        RO_count_current_annual_pace = round_off(RO_count_current_annual_pace_value)
        # Sold Hours calculations
        # [(total_CP_revenue_L_3_month['opcategory'] != 'N/A') &
        #                             (total_CP_revenue_L_3_month['opcategory'] != 'SHOP SUPPLIES')]
        Sold_hours_L_3_month = total_CP_revenue_L_3_month['lbrsoldhours'].sum()

        Sold_hours_L_3_month_avg_value = Sold_hours_L_3_month / 3
        Sold_hours_L_3_month_avg = round_off(Sold_hours_L_3_month_avg_value)
        Sold_hours_current_annual_pace = round_off(Sold_hours_L_3_month_avg_value * 12)
        # Hours per RO calculation

        if RO_count_L_3_month != 0:
            Hours_per_RO_current_annual_pace_value = Sold_hours_L_3_month / RO_count_L_3_month
            Hours_per_RO_current_annual_pace = round_off(Hours_per_RO_current_annual_pace_value)
        # ELR calculation
        Labor_sale_value_L_3_month = float(total_CP_revenue_L_3_month['lbrsale'].sum())

        if Sold_hours_L_3_month != 0:
            Total_ELR_L_3_month_value = Labor_sale_value_L_3_month / Sold_hours_L_3_month
            Total_ELR_L_3_month_avg = round_off(Total_ELR_L_3_month_value, 2)
        Total_ELR_current_annual_pace = Total_ELR_L_3_month_avg
        total_CP_revenue_L_3_month_REP = total_CP_revenue_L_3_month[total_CP_revenue_L_3_month['opcategory'] == 'REPAIR']

        if not total_CP_revenue_L_3_month_REP.empty:
            total_CP_revenue_L_3_month_REP_sale = total_CP_revenue_L_3_month_REP['lbrsale'].sum()
            total_CP_revenue_L_3_month_REP_hours = total_CP_revenue_L_3_month_REP['lbrsoldhours'].sum()

        if total_CP_revenue_L_3_month_REP_hours != 0:
            Repair_ELR_L_3_month_avg_value = total_CP_revenue_L_3_month_REP_sale / total_CP_revenue_L_3_month_REP_hours
            Repair_ELR_L_3_month_avg = round_off(Repair_ELR_L_3_month_avg_value, 2)
            Repair_ELR_current_annual_pace_value = Repair_ELR_L_3_month_avg_value
            Repair_ELR_current_annual_pace = Repair_ELR_L_3_month_avg
        #Labor Gross Profit %
        Labor_sale_value_L_3_month = float(total_CP_revenue_L_3_month['lbrsale'].sum())
        Labor_cost_value_L_3_month = float(total_CP_revenue_L_3_month['lbrcost'].sum())

        if Labor_sale_value_L_3_month != 0:
            Labor_GP_perc_L_3_month_avg_value = ((Labor_sale_value_L_3_month - Labor_cost_value_L_3_month) / Labor_sale_value_L_3_month) * 100
            Labor_GP_perc_L_3_month_avg = round_off(Labor_GP_perc_L_3_month_avg_value)
            Labor_GP_perc_current_annual_pace_value = Labor_GP_perc_L_3_month_avg_value
            Labor_GP_perc_current_annual_pace = Labor_GP_perc_L_3_month_avg
        #Parts Gross Profit %
        Parts_sale_value_L_3_month = float(total_CP_revenue_L_3_month['prtextendedsale'].sum())
        Parts_cost_value_L_3_month = float(total_CP_revenue_L_3_month['prtextendedcost'].sum())


        if Parts_sale_value_L_3_month != 0:
            Parts_GP_perc_L_3_month_avg_value = ((Parts_sale_value_L_3_month - Parts_cost_value_L_3_month) / Parts_sale_value_L_3_month) * 100
            Parts_GP_perc_L_3_month_avg = round_off(Parts_GP_perc_L_3_month_avg_value)
            Parts_GP_perc_current_annual_pace_value = Parts_GP_perc_L_3_month_avg_value
            Parts_GP_perc_current_annual_pace = Parts_GP_perc_L_3_month_avg

        #Labor Sale
        Labor_sale_L_3_month_avg_value = Labor_sale_value_L_3_month / 3
        Labor_sale_L_3_month_avg = round_off(Labor_sale_L_3_month_avg_value)
        Labor_sale_current_annual_pace_value = Labor_sale_L_3_month_avg_value * 12
        Labor_sale_current_annual_pace = round_off(Labor_sale_current_annual_pace_value)
        #Labor GP
        Labor_GP_L_3_month_avg_value = (Labor_sale_value_L_3_month - Labor_cost_value_L_3_month) / 3
        Labor_GP_L_3_month_avg = round_off(Labor_GP_L_3_month_avg_value)
        Labor_GP_current_annual_pace_value = Labor_GP_L_3_month_avg_value * 12
        Labor_GP_current_annual_pace = round_off(Labor_GP_current_annual_pace_value)
        #Parts Sale
        Parts_sale_L_3_month_avg_value = Parts_sale_value_L_3_month / 3
        Parts_sale_L_3_month_avg = round_off(Parts_sale_L_3_month_avg_value)
        Parts_sale_current_annual_pace_value = Parts_sale_L_3_month_avg_value * 12
        Parts_sale_current_annual_pace = round_off(Parts_sale_current_annual_pace_value)
        #Labor GP
        Parts_GP_L_3_month_avg_value = (Parts_sale_value_L_3_month - Parts_cost_value_L_3_month) / 3
        Parts_GP_L_3_month_avg = round_off(Parts_GP_L_3_month_avg_value)
        Parts_GP_current_annual_pace_value = Parts_GP_L_3_month_avg_value * 12
        Parts_GP_current_annual_pace = round_off(Parts_GP_current_annual_pace_value)
        #Total Labor & Parts Sale
        Labor_Parts_sale_L_3_month_avg_value = (Labor_sale_value_L_3_month + Parts_sale_value_L_3_month) / 3
        Labor_Parts_sale_L_3_month_avg = round_off(Labor_Parts_sale_L_3_month_avg_value)
        Labor_Parts_sale_current_annual_pace_value = Labor_Parts_sale_L_3_month_avg_value * 12
        Labor_Parts_sale_current_annual_pace = round_off(Labor_Parts_sale_current_annual_pace_value)
        #Total Labor & Parts GP
        Labor_Parts_GP_L_3_month_avg_value = ((Labor_sale_value_L_3_month + Parts_sale_value_L_3_month) - (Labor_cost_value_L_3_month + Parts_cost_value_L_3_month)) / 3
        Labor_Parts_GP_L_3_month_avg = round_off(Labor_Parts_GP_L_3_month_avg_value)
        Labor_Parts_GP_current_annual_pace_value = Labor_Parts_GP_L_3_month_avg_value * 12
        Labor_Parts_GP_current_annual_pace = round_off(Labor_Parts_GP_current_annual_pace_value)

    RO_count_1_month_variance = RO_count_L_month - RO_count_3_month_avg
    RO_count_annual_variance = RO_count_current_annual_pace - RO_count_prior_annual_pace
    Sold_hours_1_month_variance = round_off(Sold_hours_L_month - Sold_hours_3_month_avg)
    Sold_hours_annual_variance = round_off(Sold_hours_current_annual_pace - Sold_hours_prior_annual_pace)
    Hours_per_RO_1_month_variance = round_off(Hours_per_RO_L_month - Hours_per_RO_3_month_avg)
    Hours_per_RO_annual_variance = round_off(Hours_per_RO_current_annual_pace - Hours_per_RO_prior_annual_pace)
    Total_ELR_1_month_variance_value = Total_ELR_L_month - Total_ELR_3_month
    Total_ELR_1_month_variance = round_off(Total_ELR_1_month_variance_value, 2)
    Total_ELR_annual_variance_value = Total_ELR_L_3_month_avg - Total_ELR_3_month
    Total_ELR_annual_variance = round_off(Total_ELR_annual_variance_value, 2)
    Repair_ELR_1_month_variance_value = Repair_ELR_L_month - Repair_ELR_3_month_avg
    Repair_ELR_1_month_variance = Repair_ELR_1_month_variance_value
    Repair_ELR_annual_variance_value = Repair_ELR_L_3_month_avg - Repair_ELR_3_month_avg
    Repair_ELR_annual_variance = round_off(Repair_ELR_annual_variance_value, 2)
    Labor_GP_perc_1_month_variance = round_off(Labor_GP_perc_L_month - Labor_GP_perc_3_month_avg)
    Labor_GP_perc_annual_variance = round_off(Labor_GP_perc_current_annual_pace - Labor_GP_perc_prior_annual_pace)
    Parts_GP_perc_1_month_variance = round_off(Parts_GP_perc_L_month - Parts_GP_perc_3_month_avg)
    Parts_GP_perc_annual_variance = round_off(Parts_GP_perc_current_annual_pace - Parts_GP_perc_prior_annual_pace)
    Labor_sale_1_month_variance = round_off(Labor_sale_L_month - Labor_sale_3_month_avg)
    Labor_sale_annual_variance = round_off(Labor_sale_current_annual_pace - Labor_sale_prior_annual_pace)
    Labor_GP_1_month_variance = round_off(Labor_GP_L_month - Labor_GP_3_month_avg)
    Labor_GP_annual_variance = round_off(Labor_GP_current_annual_pace - Labor_GP_prior_annual_pace)
    Parts_sale_1_month_variance = round_off(Parts_sale_L_month - Parts_sale_3_month_avg)
    Parts_sale_annual_variance = round_off(Parts_sale_current_annual_pace - Parts_sale_prior_annual_pace)
    Parts_GP_1_month_variance = round_off(Parts_GP_L_month - Parts_GP_3_month_avg)
    Parts_GP_annual_variance = round_off(Parts_GP_current_annual_pace - Parts_GP_prior_annual_pace)

    Labor_Parts_sale_1_month_variance = round_off(Labor_Parts_sale_L_month - Labor_Parts_sale_3_month_avg)
    Labor_Parts_sale_annual_variance = round_off(Labor_Parts_sale_current_annual_pace - Labor_Parts_sale_prior_annual_pace)
    Labor_Parts_GP_1_month_variance_value = Labor_Parts_GP_L_month - Labor_Parts_GP_3_month_avg
    Labor_Parts_GP_1_month_variance = round_off(Labor_Parts_GP_1_month_variance_value)
    Labor_Parts_GP_annual_variance_value = Labor_Parts_GP_current_annual_pace - Labor_Parts_GP_prior_annual_pace
    Labor_Parts_GP_annual_variance = round_off(Labor_Parts_GP_annual_variance_value)

    #ROI Calculation based on Total Parts & Labor GP variance
    Monthly_fees = float(dms_fees + fopc_fees)
    Lbr_Pts_GP_1_month_ROI = 0
    if float(Monthly_fees) != 0:
        Lbr_Pts_GP_1_month_ROI = round_off(((float(Labor_Parts_GP_1_month_variance) - float(Monthly_fees)) / float(Monthly_fees)) * 100)

    Sold_hour_L_month_REP = round_off(total_CP_revenue_L_month[total_CP_revenue_L_month['opcategory'] == 'REPAIR']['lbrsoldhours'].sum())
    Rep_ELR_1_month_ROI = 0
    if float(Monthly_fees) != 0:
        Rep_ELR_1_month_ROI = round_off((round_off((float(Repair_ELR_1_month_variance) * float(Sold_hour_L_month_REP)) - float(Monthly_fees)) / float(Monthly_fees)) * 100)

    Lbr_Pts_GP_annual_ROI = 0
    if float(Monthly_fees) != 0:
        Lbr_Pts_GP_annual_ROI = round_off(((float(Labor_Parts_GP_annual_variance) - (float(Monthly_fees) * 12)) / (float(Monthly_fees) * 12)) * 100)


    Sold_hour_L_3_month_REP = ((total_CP_revenue_L_3_month[total_CP_revenue_L_3_month['opcategory'] == 'REPAIR']['lbrsoldhours'].sum()) / 3) * 12
    Rep_ELR_annual_ROI = 0
    if float(Monthly_fees) != 0:
        Rep_ELR_annual_ROI = round_off(((round_off(float(Repair_ELR_annual_variance) * round_off(Sold_hour_L_3_month_REP)) - (float(Monthly_fees)) * 12) / (float(Monthly_fees) * 12)) * 100)

    Client_Report_Card ={"Monthly FOPC": float(fopc_fees),
                        "Monthly DMS": float(dms_fees),
                        "Total": Monthly_fees,
                        "ROI": Lbr_Pts_GP_1_month_ROI,
                        "Total Pts & Lbr GP Change": Labor_Parts_GP_1_month_variance,
                        "Repair ELR Change":round_off((float(Repair_ELR_1_month_variance)) * round_off(Sold_hour_L_month_REP)),
                        "Total Pts & Lbr GP Change_Annualized":Labor_Parts_GP_annual_variance,
                        "Repair ELR Change_Annualized":round_off(float(Repair_ELR_annual_variance) * round_off(Sold_hour_L_3_month_REP)),
                        "KPIs": {
                        "RO Count": [RO_count_3_month_avg, RO_count_L_month, RO_count_1_month_variance, RO_count_prior_annual_pace, RO_count_current_annual_pace, RO_count_annual_variance],
                        "Hours Sold": [Sold_hours_3_month_avg, Sold_hours_L_month, Sold_hours_1_month_variance, Sold_hours_prior_annual_pace, Sold_hours_current_annual_pace, Sold_hours_annual_variance],
                        "Cust. Pay Hrs Per RO": [Hours_per_RO_3_month_avg, Hours_per_RO_L_month, Hours_per_RO_1_month_variance, Hours_per_RO_prior_annual_pace, Hours_per_RO_current_annual_pace, Hours_per_RO_annual_variance],
                        "Total Shop ELR": [Total_ELR_3_month, Total_ELR_L_month, Total_ELR_1_month_variance, Total_ELR_prior_annual_pace, Total_ELR_current_annual_pace, Total_ELR_annual_variance],
                        "Total Labor GP%":[Labor_GP_perc_3_month_avg, Labor_GP_perc_L_month, Labor_GP_perc_1_month_variance, Labor_GP_perc_prior_annual_pace, Labor_GP_perc_current_annual_pace, Labor_GP_perc_annual_variance],
                        "Total Parts GP%":[Parts_GP_perc_3_month_avg, Parts_GP_perc_L_month, Parts_GP_perc_1_month_variance, Parts_GP_perc_prior_annual_pace, Parts_GP_perc_current_annual_pace, Parts_GP_perc_annual_variance],
                        "Total Labor Sold":[Labor_sale_3_month_avg, Labor_sale_L_month, Labor_sale_1_month_variance, Labor_sale_prior_annual_pace, Labor_sale_current_annual_pace, Labor_sale_annual_variance],
                        "Total Labor GP":[Labor_GP_3_month_avg, Labor_GP_L_month, Labor_GP_1_month_variance, Labor_GP_prior_annual_pace, Labor_GP_current_annual_pace, Labor_GP_annual_variance],
                        "Total Parts Sale":[Parts_sale_3_month_avg, Parts_sale_L_month, Parts_sale_1_month_variance, Parts_sale_prior_annual_pace, Parts_sale_current_annual_pace, Parts_sale_annual_variance],
                        "Total Parts GP":[Parts_GP_3_month_avg, Parts_GP_L_month, Parts_GP_1_month_variance, Parts_GP_prior_annual_pace, Parts_GP_current_annual_pace, Parts_GP_annual_variance],
                        "Total Lbr & Pts Sales":[Labor_Parts_sale_3_month_avg, Labor_Parts_sale_L_month, Labor_Parts_sale_1_month_variance, Labor_Parts_sale_prior_annual_pace, Labor_Parts_sale_current_annual_pace, Labor_Parts_sale_annual_variance],
                        "Total Lbr & Pts GP":[Labor_Parts_GP_3_month_avg, Labor_Parts_GP_L_month, Labor_Parts_GP_1_month_variance, Labor_Parts_GP_prior_annual_pace, Labor_Parts_GP_current_annual_pace, Labor_Parts_GP_annual_variance]
    },
    "Competitive": {},
    "Maintenance": {},
    "Repair": {}
    }

    group_by_category_3_month = total_CP_revenue_3_month.groupby(['opcategory']).agg({
        'lbrsale' : 'sum',
        'lbrcost' : 'sum',
        'prtextendedsale' : 'sum',
        'prtextendedcost' : 'sum',
        'lbrsoldhours' : 'sum'
    }).reset_index()

    group_by_category_3_month = group_by_category_3_month.astype(float, errors='ignore')

    if not group_by_category_3_month.empty:
        for index, rows in group_by_category_3_month.iterrows():
            group_by_category_3_month.at[index, "ELR"] = 0
            if rows['lbrsoldhours'] != 0:
                group_by_category_3_month.at[index, "ELR"] = rows['lbrsale'] / rows['lbrsoldhours']

            group_by_category_3_month.at[index, "Percentage of Sold Hours"] = 0
            if Sold_hours_3_month != 0:
                group_by_category_3_month.at[index, "Percentage of Sold Hours"] = (rows['lbrsoldhours'] / Sold_hours_3_month) * 100

            group_by_category_3_month.at[index, "Labor GP"] = rows['lbrsale'] - rows['lbrcost']

            group_by_category_3_month.at[index, "Labor GP %"] = 0
            if rows['lbrsale'] != 0:
                group_by_category_3_month.at[index, "Labor GP %"] = ((rows['lbrsale'] - rows['lbrcost']) / rows['lbrsale']) * 100

            group_by_category_3_month.at[index, "Parts GP"] = rows['prtextendedsale'] - rows['prtextendedcost']

            group_by_category_3_month.at[index, "Parts GP %"] = 0
            if rows['prtextendedsale'] != 0:
                group_by_category_3_month.at[index, "Parts GP %"] = ((rows['prtextendedsale'] - rows['prtextendedcost']) / rows['prtextendedsale']) * 100

            group_by_category_3_month.at[index, "Total Lbr & Pts Sale"] = rows['lbrsale'] + rows['prtextendedsale']

            group_by_category_3_month.at[index, "Total Lbr & Pts GP"] = (rows['lbrsale'] + rows['prtextendedsale']) - (rows['lbrcost'] + rows['prtextendedcost'])

    group_by_category_L_month = total_CP_revenue_L_month.groupby(['opcategory']).agg({
        'lbrsale' : 'sum',
        'lbrcost' : 'sum',
        'prtextendedsale' : 'sum',
        'prtextendedcost' : 'sum',
        'lbrsoldhours' : 'sum'
    }).reset_index()

    group_by_category_L_month = group_by_category_L_month.astype(float, errors='ignore')

    if not group_by_category_L_month.empty:
        for index, rows in group_by_category_L_month.iterrows():
            group_by_category_L_month.at[index, "ELR"] = 0
            if rows['lbrsoldhours'] != 0:
                group_by_category_L_month.at[index, "ELR"] = rows['lbrsale'] / rows['lbrsoldhours']

            group_by_category_L_month.at[index, "Percentage of Sold Hours"] = 0
            if Sold_hours_L_month != 0:
                group_by_category_L_month.at[index, "Percentage of Sold Hours"] = (rows['lbrsoldhours'] / Sold_hours_L_month) * 100

            group_by_category_L_month.at[index, "Labor GP"] = rows['lbrsale'] - rows['lbrcost']

            group_by_category_L_month.at[index, "Labor GP %"] = 0
            if rows['lbrsale'] != 0:
                group_by_category_L_month.at[index, "Labor GP %"] = ((rows['lbrsale'] - rows['lbrcost']) / rows['lbrsale']) * 100

            group_by_category_L_month.at[index, "Parts GP"] = rows['prtextendedsale'] - rows['prtextendedcost']

            group_by_category_L_month.at[index, "Parts GP %"] = 0
            if rows['prtextendedsale'] != 0:
                group_by_category_L_month.at[index, "Parts GP %"] = ((rows['prtextendedsale'] - rows['prtextendedcost']) / rows['prtextendedsale']) * 100

            group_by_category_L_month.at[index, "Total Lbr & Pts Sale"] = rows['lbrsale'] + rows['prtextendedsale']

            group_by_category_L_month.at[index, "Total Lbr & Pts GP"] = (rows['lbrsale'] + rows['prtextendedsale']) - (rows['lbrcost'] + rows['prtextendedcost'])

    group_by_category_L_3_month = total_CP_revenue_L_3_month.groupby(['opcategory']).agg({
        'lbrsale' : 'sum',
        'lbrcost' : 'sum',
        'prtextendedsale' : 'sum',
        'prtextendedcost' : 'sum',
        'lbrsoldhours' : 'sum'
    }).reset_index()

    group_by_category_L_3_month = group_by_category_L_3_month.astype(float, errors='ignore')

    if not group_by_category_L_3_month.empty:
        for index, rows in group_by_category_L_3_month.iterrows():
            group_by_category_L_3_month.at[index, "ELR"] = 0
            if rows['lbrsoldhours'] != 0:
                group_by_category_L_3_month.at[index, "ELR"] = rows['lbrsale'] / rows['lbrsoldhours']

            group_by_category_L_3_month.at[index, "Percentage of Sold Hours"] = 0
            if Sold_hours_L_3_month != 0:
                group_by_category_L_3_month.at[index, "Percentage of Sold Hours"] = (rows['lbrsoldhours'] / Sold_hours_L_3_month) * 100

            group_by_category_L_3_month.at[index, "Labor GP"] = rows['lbrsale'] - rows['lbrcost']

            group_by_category_L_3_month.at[index, "Labor GP %"] = 0
            if rows['lbrsale'] != 0:
                group_by_category_L_3_month.at[index, "Labor GP %"] = ((rows['lbrsale'] - rows['lbrcost']) / rows['lbrsale']) * 100

            group_by_category_L_3_month.at[index, "Parts GP"] = rows['prtextendedsale'] - rows['prtextendedcost']

            group_by_category_L_3_month.at[index, "Parts GP %"] = 0
            if rows['prtextendedsale'] != 0:
                group_by_category_L_3_month.at[index, "Parts GP %"] = ((rows['prtextendedsale'] - rows['prtextendedcost']) / rows['prtextendedsale']) * 100

            group_by_category_L_3_month.at[index, "Total Lbr & Pts Sale"] = rows['lbrsale'] + rows['prtextendedsale']

            group_by_category_L_3_month.at[index, "Total Lbr & Pts GP"] = (rows['lbrsale'] + rows['prtextendedsale']) - (rows['lbrcost'] + rows['prtextendedcost'])


    special_cases = {}
    opcategory_list = ['COMPETITIVE','MAINTENANCE','REPAIR']
    for cat in opcategory_list:
        # 3 month average values
        hours_sold_3_month_avg_value = float(group_by_category_3_month[group_by_category_3_month['opcategory'] == cat]['lbrsoldhours'].iloc[0] / 3)
        hours_sold_3_month_avg = round_off(hours_sold_3_month_avg_value)
        ELR_3_month_value = float(group_by_category_3_month[group_by_category_3_month['opcategory'] == cat]['ELR'].iloc[0])
        ELR_3_month = round_off(ELR_3_month_value, 2)
        sld_hours_perc_3_month_value = float(group_by_category_3_month[group_by_category_3_month['opcategory'] == cat]['Percentage of Sold Hours'].iloc[0])
        sld_hours_perc_3_month = round_off(sld_hours_perc_3_month_value)
        labor_sale_3_month_avg_value = float(group_by_category_3_month[group_by_category_3_month['opcategory'] == cat]['lbrsale'].iloc[0] / 3)
        labor_sale_3_month_avg = round_off(labor_sale_3_month_avg_value)
        labor_GP_perc_3_month_value = group_by_category_3_month[group_by_category_3_month['opcategory'] == cat]['Labor GP %'].iloc[0]
        labor_GP_perc_3_month = round_off(labor_GP_perc_3_month_value)
        labor_GP_3_month_avg_value = float(group_by_category_3_month[group_by_category_3_month['opcategory'] == cat]['Labor GP'].iloc[0] / 3)
        labor_GP_3_month_avg = round_off(labor_GP_3_month_avg_value)
        parts_sale_3_month_avg_value = float(group_by_category_3_month[group_by_category_3_month['opcategory'] == cat]['prtextendedsale'].iloc[0] / 3)
        parts_sale_3_month_avg = round_off(parts_sale_3_month_avg_value)
        parts_GP_3_month_avg_value = float(group_by_category_3_month[group_by_category_3_month['opcategory'] == cat]['Parts GP'].iloc[0] / 3)
        parts_GP_3_month_avg = round_off(parts_GP_3_month_avg_value)
        parts_GP_perc_3_month_value = float(group_by_category_3_month[group_by_category_3_month['opcategory'] == cat]['Parts GP %'].iloc[0])
        parts_GP_perc_3_month = round_off(parts_GP_perc_3_month_value)
        total_lbr_pts_sale_3_month_avg_value = float(group_by_category_3_month[group_by_category_3_month['opcategory'] == cat]['Total Lbr & Pts Sale'].iloc[0] / 3)
        total_lbr_pts_sale_3_month_avg = round_off(total_lbr_pts_sale_3_month_avg_value)
        total_lbr_pts_GP_3_month_avg_value = float(group_by_category_3_month[group_by_category_3_month['opcategory'] == cat]['Total Lbr & Pts GP'].iloc[0] / 3)
        total_lbr_pts_GP_3_month_avg = round_off(total_lbr_pts_GP_3_month_avg_value)


        # L month average values
        hours_sold_L_month_value = float(group_by_category_L_month[group_by_category_L_month['opcategory'] == cat]['lbrsoldhours'].iloc[0])
        hours_sold_L_month = round_off(hours_sold_L_month_value)
        ELR_L_month_value = float(group_by_category_L_month[group_by_category_L_month['opcategory'] == cat]['ELR'].iloc[0])
        ELR_L_month = round_off(ELR_L_month_value, 2)
        sld_hours_perc_L_month_value = float(group_by_category_L_month[group_by_category_L_month['opcategory'] == cat]['Percentage of Sold Hours'].iloc[0])
        sld_hours_perc_L_month = round_off(sld_hours_perc_L_month_value)
        labor_sale_L_month_value = float(group_by_category_L_month[group_by_category_L_month['opcategory'] == cat]['lbrsale'].iloc[0])
        labor_sale_L_month = round_off(labor_sale_L_month_value)
        labor_GP_perc_L_month_value = group_by_category_L_month[group_by_category_L_month['opcategory'] == cat]['Labor GP %'].iloc[0]
        labor_GP_perc_L_month = round_off(labor_GP_perc_L_month_value)
        labor_GP_L_month_value = float(group_by_category_L_month[group_by_category_L_month['opcategory'] == cat]['Labor GP'].iloc[0])
        labor_GP_L_month = round_off(labor_GP_L_month_value)
        parts_sale_L_month_value = float(group_by_category_L_month[group_by_category_L_month['opcategory'] == cat]['prtextendedsale'].iloc[0])
        parts_sale_L_month = round_off(parts_sale_L_month_value)
        parts_GP_L_month_value = float(group_by_category_L_month[group_by_category_L_month['opcategory'] == cat]['Parts GP'].iloc[0])
        parts_GP_L_month = round_off(parts_GP_L_month_value)
        parts_GP_perc_L_month_value = float(group_by_category_L_month[group_by_category_L_month['opcategory'] == cat]['Parts GP %'].iloc[0])
        parts_GP_perc_L_month = round_off(parts_GP_perc_L_month_value)
        total_lbr_pts_sale_L_month_value = float(group_by_category_L_month[group_by_category_L_month['opcategory'] == cat]['Total Lbr & Pts Sale'].iloc[0])
        total_lbr_pts_sale_L_month = round_off(total_lbr_pts_sale_L_month_value)
        total_lbr_pts_GP_L_month_value = float(group_by_category_L_month[group_by_category_L_month['opcategory'] == cat]['Total Lbr & Pts GP'].iloc[0])
        total_lbr_pts_GP_L_month = round_off(total_lbr_pts_GP_L_month_value)

        # Baseline annual values
        hours_sold_annual_3_month_value = float((group_by_category_3_month[group_by_category_3_month['opcategory'] == cat]['lbrsoldhours'].iloc[0] / 3) * 12)
        hours_sold_annual_3_month = round_off(hours_sold_annual_3_month_value)
        ELR_annual_3_month_value = float(group_by_category_3_month[group_by_category_3_month['opcategory'] == cat]['ELR'].iloc[0])
        ELR_annual_3_month = round_off(ELR_3_month_value, 2)
        sld_hours_annual_perc_3_month_value = float(group_by_category_3_month[group_by_category_3_month['opcategory'] == cat]['Percentage of Sold Hours'].iloc[0])
        sld_hours_annual_perc_3_month = round_off(sld_hours_perc_3_month_value)
        labor_sale_annual_3_month_value = float((group_by_category_3_month[group_by_category_3_month['opcategory'] == cat]['lbrsale'].iloc[0] / 3) * 12)
        labor_sale_annual_3_month = round_off(labor_sale_annual_3_month_value)
        labor_GP_perc_annual_3_month_value = group_by_category_3_month[group_by_category_3_month['opcategory'] == cat]['Labor GP %'].iloc[0]
        labor_GP_perc_annual_3_month = round_off(labor_GP_perc_annual_3_month_value)
        labor_GP_annual_3_month_value = float((group_by_category_3_month[group_by_category_3_month['opcategory'] == cat]['Labor GP'].iloc[0] / 3) *12)
        labor_GP_annual_3_month = round_off(labor_GP_annual_3_month_value)
        parts_sale_annual_3_month_value = float((group_by_category_3_month[group_by_category_3_month['opcategory'] == cat]['prtextendedsale'].iloc[0] / 3) *12)
        parts_sale_annual_3_month = round_off(parts_sale_annual_3_month_value)
        parts_GP_annual_3_month_value = float((group_by_category_3_month[group_by_category_3_month['opcategory'] == cat]['Parts GP'].iloc[0] / 3) * 12)
        parts_GP_annual_3_month = round_off(parts_GP_annual_3_month_value)
        parts_GP_perc_annual_3_month_value = float(group_by_category_3_month[group_by_category_3_month['opcategory'] == cat]['Parts GP %'].iloc[0])
        parts_GP_perc_annual_3_month = round_off(parts_GP_perc_3_month_value)
        total_lbr_pts_sale_annual_3_month_value = float((group_by_category_3_month[group_by_category_3_month['opcategory'] == cat]['Total Lbr & Pts Sale'].iloc[0] / 3) * 12)
        total_lbr_pts_sale_annual_3_month = round_off(total_lbr_pts_sale_annual_3_month_value)
        total_lbr_pts_GP_annual_3_month_value = float((group_by_category_3_month[group_by_category_3_month['opcategory'] == cat]['Total Lbr & Pts GP'].iloc[0] / 3) * 12)
        total_lbr_pts_GP_annual_3_month = round_off(total_lbr_pts_GP_annual_3_month_value)

        # L 3 month annual values
        hours_sold_L_3_month_value = float((group_by_category_L_3_month[group_by_category_L_3_month['opcategory'] == cat]['lbrsoldhours'].iloc[0] / 3) * 12)
        hours_sold_L_3_month = round_off(hours_sold_L_3_month_value)
        ELR_L_3_month_value = float(group_by_category_L_3_month[group_by_category_L_3_month['opcategory'] == cat]['ELR'].iloc[0])
        ELR_L_3_month = round_off(ELR_L_3_month_value, 2)
        sld_hours_perc_L_3_month_value = float(group_by_category_L_3_month[group_by_category_L_3_month['opcategory'] == cat]['Percentage of Sold Hours'].iloc[0])
        sld_hours_perc_L_3_month = round_off(sld_hours_perc_L_3_month_value)
        labor_sale_L_3_month_value = float((group_by_category_L_3_month[group_by_category_L_3_month['opcategory'] == cat]['lbrsale'].iloc[0] / 3) * 12)
        labor_sale_L_3_month = round_off(labor_sale_L_3_month_value)
        labor_GP_perc_L_3_month_value = group_by_category_L_3_month[group_by_category_L_3_month['opcategory'] == cat]['Labor GP %'].iloc[0]
        labor_GP_perc_L_3_month = round_off(labor_GP_perc_L_3_month_value)
        labor_GP_L_3_month_value = float((group_by_category_L_3_month[group_by_category_L_3_month['opcategory'] == cat]['Labor GP'].iloc[0] / 3) *12)
        labor_GP_L_3_month = round_off(labor_GP_L_3_month_value)
        parts_sale_L_3_month_value = float((group_by_category_L_3_month[group_by_category_L_3_month['opcategory'] == cat]['prtextendedsale'].iloc[0] / 3) *12)
        parts_sale_L_3_month = round_off(parts_sale_L_3_month_value)
        parts_GP_L_3_month_value = float((group_by_category_L_3_month[group_by_category_L_3_month['opcategory'] == cat]['Parts GP'].iloc[0] / 3) * 12)
        parts_GP_L_3_month = round_off(parts_GP_L_3_month_value)
        parts_GP_perc_L_3_month_value = float(group_by_category_L_3_month[group_by_category_L_3_month['opcategory'] == cat]['Parts GP %'].iloc[0])
        parts_GP_perc_L_3_month = round_off(parts_GP_perc_L_3_month_value)
        total_lbr_pts_sale_L_3_month_value = float((group_by_category_L_3_month[group_by_category_L_3_month['opcategory'] == cat]['Total Lbr & Pts Sale'].iloc[0] / 3) * 12)
        total_lbr_pts_sale_L_3_month = round_off(total_lbr_pts_sale_L_3_month_value)
        total_lbr_pts_GP_L_3_month_value = float((group_by_category_L_3_month[group_by_category_L_3_month['opcategory'] == cat]['Total Lbr & Pts GP'].iloc[0] / 3) * 12)
        total_lbr_pts_GP_L_3_month = round_off(total_lbr_pts_GP_L_3_month_value)

            # Create dictionary for special cases
        special_cases = {
            "Hours Sold": {
                "3 MTH Avg (Baseline)":hours_sold_3_month_avg ,
                "Last Month": hours_sold_L_month,
                "Variance":round_off(hours_sold_L_month - hours_sold_3_month_avg) ,
                "Prior Annual Pace":hours_sold_annual_3_month ,
                "Annual Pace":hours_sold_L_3_month ,
                "Variance Annualized":round_off(hours_sold_L_3_month - hours_sold_annual_3_month)

            },
            "ELR": {
                    "3 MTH Avg (Baseline)": ELR_3_month,
                "Last Month":ELR_L_month,
                "Variance": round_off((ELR_L_month - ELR_3_month), 2),
                "Prior Annual Pace": ELR_annual_3_month,
                "Annual Pace":ELR_L_3_month ,
                "Variance Annualized":round_off((ELR_L_3_month - ELR_annual_3_month), 2)
            },
            "Total Labor GP%": {
                "3 MTH Avg (Baseline)": labor_GP_perc_3_month,
                "Last Month":labor_GP_perc_L_month ,
                "Variance": round_off(labor_GP_perc_L_month - labor_GP_perc_3_month),
                "Prior Annual Pace": labor_GP_perc_annual_3_month,
                "Annual Pace": labor_GP_perc_L_3_month,
                "Variance Annualized":round_off(labor_GP_perc_L_3_month - labor_GP_perc_annual_3_month)
            },
            "Total Labor Sold": {
                "3 MTH Avg (Baseline)": labor_sale_3_month_avg,
                "Last Month":labor_sale_L_month,
                "Variance":round_off(labor_sale_L_month - labor_sale_3_month_avg),
                "Prior Annual Pace": labor_sale_annual_3_month,
                "Annual Pace": labor_sale_L_3_month,
                "Variance Annualized": round_off(labor_sale_L_3_month - labor_sale_annual_3_month)
            },
            "% of Total Shop Hours":{
                "3 MTH Avg (Baseline)": sld_hours_perc_3_month,
                "Last Month": sld_hours_perc_L_month,
                "Variance":round_off(sld_hours_perc_L_month - sld_hours_perc_3_month),
                "Prior Annual Pace": sld_hours_annual_perc_3_month,
                "Annual Pace": sld_hours_perc_L_3_month,
                "Variance Annualized":  round_off(sld_hours_perc_L_3_month - sld_hours_annual_perc_3_month)

                },

            "Total Labor GP":{
                "3 MTH Avg (Baseline)": labor_GP_3_month_avg,
                "Last Month": labor_GP_L_month,
                "Variance":round_off(labor_GP_L_month - labor_GP_3_month_avg),
                "Prior Annual Pace": labor_GP_annual_3_month,
                "Annual Pace": labor_GP_L_3_month,
                "Variance Annualized":  round_off(labor_GP_L_3_month - labor_GP_annual_3_month)


                },

                "Total Parts Sale":{
                "3 MTH Avg (Baseline)": parts_sale_3_month_avg,
                "Last Month": parts_sale_L_month,
                "Variance": round_off(parts_sale_L_month - parts_sale_3_month_avg),
                "Prior Annual Pace": parts_sale_annual_3_month,
                "Annual Pace": parts_sale_L_3_month,
                "Variance Annualized":   round_off(parts_sale_L_3_month - parts_sale_annual_3_month)


                },

            "Total Parts GP":{
                "3 MTH Avg (Baseline)": parts_GP_3_month_avg,
                "Last Month": parts_GP_L_month,
                "Variance": round_off(parts_GP_L_month - parts_GP_3_month_avg),
                "Prior Annual Pace": parts_GP_annual_3_month,
                "Annual Pace": parts_GP_L_3_month,
                "Variance Annualized":   round_off(parts_GP_L_3_month - parts_GP_annual_3_month)


                },
            "Total Parts GP%":{
                "3 MTH Avg (Baseline)": parts_GP_perc_3_month,
                "Last Month": parts_GP_perc_L_month,
                "Variance": round_off(parts_GP_perc_L_month - parts_GP_perc_3_month),
                "Prior Annual Pace": parts_GP_perc_annual_3_month,
                "Annual Pace": parts_GP_perc_L_3_month,
                "Variance Annualized":  round_off(parts_GP_perc_L_3_month - parts_GP_perc_annual_3_month)
                        },
            "Lbr & Pts GP": {
                "3 MTH Avg (Baseline)":total_lbr_pts_GP_3_month_avg,
                "Last Month":total_lbr_pts_GP_L_month,
                "Variance": round_off(total_lbr_pts_GP_L_month - total_lbr_pts_GP_3_month_avg),
                "Prior Annual Pace":total_lbr_pts_GP_annual_3_month,
                "Annual Pace": total_lbr_pts_GP_L_3_month,
                "Variance Annualized": round_off(total_lbr_pts_GP_L_3_month - total_lbr_pts_GP_annual_3_month)
                },

            "Total Lbr & Pts Sales": {
                    "3 MTH Avg (Baseline)": total_lbr_pts_sale_3_month_avg,
                "Last Month": total_lbr_pts_sale_L_month,
                "Variance":  round_off(total_lbr_pts_sale_L_month - total_lbr_pts_sale_3_month_avg),
                "Prior Annual Pace": total_lbr_pts_sale_annual_3_month,
                "Annual Pace": total_lbr_pts_sale_L_3_month,
                "Variance Annualized":   round_off(total_lbr_pts_sale_L_3_month - total_lbr_pts_sale_annual_3_month)
            }
        }

        #  Update Client_Report_Card
        Client_Report_Card[cat] = special_cases

    # Convert DataFrame to dictionary (records format is usually preferred)
    if isinstance(Client_Report_Card, pd.DataFrame):
        Client_Report_Card = Client_Report_Card.to_dict(orient="records")

    # Build the output path
    json_output_path = os.path.join(result_folder, "client_report_card_3_months.json")

    # Save the Client_Report_Card data to the JSON file
    with open(json_output_path, "w") as json_file:
        json.dump(Client_Report_Card, json_file, indent=4)

    # Define relative image file names (can still be comma-separated in .env if needed)
    IMAGE_PATHS = "client_report_card_3_months1.jpg,client_report_card_3_months2.jpg"
    img_files = IMAGE_PATHS.strip().split(",")

    # Clean and convert to full paths inside the result folder
    clean_paths = [
        os.path.join(result_folder, path.strip().replace('\x0c', '\f').replace('\n', ''))
        for path in img_files
    ]

    # Call the image extraction function
    extract_image_data_split(CUSTOM_SYSTEM_PROMPT, clean_paths)

    # File paths
    file1_path = os.path.join(result_folder, "client_report_card_3_months1.md")
    file2_path = os.path.join(result_folder, "client_report_card_3_months2.md")

    # Load JSON from primary and secondary files
    with open(file1_path, "r") as f:
        primary_data = json.load(f)

    with open(file2_path, "r") as f:
        secondary_data = json.load(f)

    filled_screen1 = merge_missing_values(primary_data, secondary_data)

    # Save updated data back to file1
    with open(file1_path, "w") as f:
        json.dump(filled_screen1, f, indent=2)

    # Set the output path for comparison
    output_md_file_path = file1_path

    # compare the calculated values and UI values
    compare_client_report_card_3_months(output_md_file_path, json_output_path)
    print("Completed the validation of Client Report Card 3 months")