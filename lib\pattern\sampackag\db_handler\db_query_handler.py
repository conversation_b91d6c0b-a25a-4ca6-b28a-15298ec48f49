import os
from datetime import datetime
from dateutil.relativedelta import relativedelta
from dotenv import load_dotenv
from lib.pattern.config import config

load_dotenv()

def get_store_id():
    store_id_set = config.store_id

    if store_id_set is None:
        raise ValueError("store_id is not set in config")

    if store_id_set == 'all':
        return 'all'
    elif ',' in store_id_set:
        return f"({', '.join(repr(x.strip()) for x in store_id_set.split(','))})"
    else:
        return f"('{store_id_set.strip()}')"

def get_start_date():
    return config.start_date

def get_end_date():
    return config.end_date

def get_fopc_month():
    return config.fopc_month

def get_pre_fopc_month():
    return config.pre_fopc_month

def get_last_month():
    return config.last_month

def get_date_ranges_sql():
    last_month = get_last_month()
    if last_month is not None:
        last_month_date = datetime.strptime(last_month, "%Y-%m")
        date_ranges = [(last_month_date - relativedelta(months=i)).strftime("%Y-%m") for i in range(3)]
        date_ranges_sql = "({})".format(", ".join([f"'{date}'" for date in date_ranges]))
        return date_ranges_sql

# -------- Query classes --------
class OpcodeQuerygenerator:
    def generate_query(self):
        return f"SELECT department,opcode,opcategory,store_id,grid_excluded FROM stateful_cc_physical_rw.ro_opcodes WHERE store_id in {get_store_id()};"

class payTypeFixedRateUpdateStatus:
    def generate_query(self):
        return f"SELECT id, opcode, pay_type, start_date, end_date, store_id FROM stateful_cc_physical_rw.fixed_rate_enable_disable_master_log WHERE id IS NOT NULL AND store_id in {get_store_id()};"

class payTypeFixedRateTable:
    def generate_query(self):
        return f"SELECT id, paytype, labor_fixedratevalue, parts_fixedratevalue, store_id, fixedratedate FROM stateful_cc_physical_rw.fixed_rate_master_paytype WHERE store_id in {get_store_id()};"

class opcodeFixedRateTable:
    def generate_query(self):
        return f"SELECT id, opcode, paytype, fixed_rate_value, store_id, fixed_rate_date FROM stateful_cc_physical_rw.fixed_rate_master WHERE store_id in {get_store_id()};"

class gridDataTable:
    def generate_query(self):
        return f"SELECT hours, col_0, col_1, col_2, col_3, col_4, col_5, col_6, col_7, col_8, col_9, store_id, created_date, door_rate, grid_type FROM stateful_cc_physical_rw.griddata_dtl WHERE store_id in {get_store_id()};"

class OpcodeTableQuery:
    def generate_query(self):
        return f"SELECT department,opcode,opcategory,grid_excluded,store_id, mpi_item FROM stateful_cc_physical_rw.ro_opcodes WHERE store_id in {get_store_id()};"

class partInventoryDetailsQuery:
    def generate_query(self):
        return f"SELECT id,partno,part_source,list_price,store_id FROM stateful_atm_source_raw.parts_inventory_details WHERE store_id in {get_store_id()};"

class MenuOpcodes:
    def get_menu_opcodes(self):
        return f"SELECT opcode FROM stateful_cc_physical_rw.menu_opcodes WHERE store_id in {get_store_id()};"

class payTypeMasterTableQuery:
    def generate_query(self):
        return f"SELECT pay_type,department,store_id FROM stateful_cc_physical_rw.pay_type_master WHERE store_id in {get_store_id()};"

class menuMasterTableQuery:
    def generate_query(self):
        return f"SELECT * FROM stateful_cc_physical_rw.menu_master WHERE store_id in {get_store_id()};"

class menuServiceTypeTableQuery:
    def generate_query(self):
        return f"SELECT * FROM stateful_cc_physical_rw.menu_service_type WHERE store_id in {get_store_id()};"

class assignedMenuModelsTableQuery:
    def generate_query(self):
        return f"SELECT * FROM stateful_cc_physical_rw.assigned_menu_models WHERE store_id in {get_store_id()};"

class assignedMenuOpcodesTableQuery:
    def generate_query(self):
        return f"SELECT * FROM stateful_cc_physical_rw.assigned_menu_opcodes WHERE store_id in {get_store_id()};"

class MPISetupTableQuery:
    def generate_query(self):
        return f"SELECT frh,is_active FROM stateful_cc_physical_rw.mpi_setup WHERE store_id in {get_store_id()};"

class MPIOpcodesTableQuery:
    def generate_query(self):
        return f"SELECT opcode FROM stateful_cc_physical_rw.mpi_opcodes WHERE store_id in {get_store_id()};"

class fleetCustomerFixedRateTableQuery:
    def generate_query(self):
        return f"SELECT * FROM stateful_cc_physical_rw.labor_grid_fleet_account WHERE store_id in {get_store_id()};"

class fleetPayTypeFixedRateTableQuery:
    def generate_query(self):
        return f"SELECT * FROM stateful_cc_physical_rw.labor_grid_fleet_paytype WHERE store_id in {get_store_id()};"

class fleetOpcodeFixedRateTableQuery:
    def generate_query(self):
        return f"SELECT * FROM stateful_cc_physical_rw.labor_grid_fleet_opcode WHERE store_id in {get_store_id()};"

class gridDataDetailsQuery:
    def generate_query(self):
        return f"SELECT * FROM stateful_cc_physical_rw.griddata_dtl WHERE store_id in {get_store_id()};"

class allRevenueDetailsTableQuery:
    def generate_query(self):
        store_id_sql = get_store_id()
        start_date = get_start_date()
        end_date = get_end_date()
        
        # If store_id is 'all', modify the SQL accordingly
        if store_id_sql == 'all':
            return (f"SELECT * FROM stateful_cc_aggregate.all_revenue_details "
                    f"WHERE closeddate >= '{start_date}' AND closeddate <= '{end_date}';")
        else:
            return (f"SELECT * FROM stateful_cc_aggregate.all_revenue_details "
                    f"WHERE store_id IN {store_id_sql} "
                    f"AND closeddate >= '{start_date}' AND closeddate <= '{end_date}';")
        
class allRevenueDetailsForClientReportCardQuery:
    def generate_query(self):
        sid = get_store_id()
        if sid == 'all':
            return f"SELECT * FROM stateful_cc_aggregate.all_revenue_details WHERE month_year IN ('{fopc_month}' ,'{pre_fopc_month}');"
        return f"SELECT * FROM stateful_cc_aggregate.all_revenue_details WHERE store_id in {sid} and month_year IN ('{fopc_month}' ,'{pre_fopc_month}');"

class allRevenueDetailsForClientReportCard3MonthQuery:
    def generate_query(self):
        sid = get_store_id()
        s_date_obj = datetime.strptime(get_start_date(), "%Y-%m-%d")
        e_date_obj = datetime.strptime(get_end_date(), "%Y-%m-%d")
        s_date_env = s_date_obj.strftime("%Y-%m")
        e_date_env = e_date_obj.strftime("%Y-%m")
        date_ranges_sql = get_date_ranges_sql()
        if sid == 'all':
            return f"SELECT * FROM stateful_cc_aggregate.all_revenue_details WHERE ((month_year >= '{s_date_env}' AND month_year <= '{e_date_env}') OR month_year IN {date_ranges_sql});"
        return f"SELECT * FROM stateful_cc_aggregate.all_revenue_details WHERE store_id in {sid} AND ((month_year >= '{s_date_env}' AND month_year <= '{e_date_env}') OR month_year IN {date_ranges_sql});"

class getCustomerPayTypeGroups:
    def generate_query(self):
        return f"SELECT source_paytype, store_id FROM stateful_cc_physical_rw.paytype_retail_flag_setting prfs WHERE store_id in {get_store_id()} AND mapped_paytype = 'C';"
