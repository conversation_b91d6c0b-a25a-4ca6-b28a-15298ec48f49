"""
Compares KPI dashboard values between UI and calculated results, and generates CSV, Excel, JSON, and HTML reports.
"""

import os
import json
import csv
import logging
from collections import defaultdict
from datetime import datetime
from typing import Any, Dict, List

import numpy as np
import openpyxl
from openpyxl.styles import Font, Alignment, PatternFill
from dotenv import load_dotenv

from lib.pattern.config import config
from lib.std.universal.utils import create_folder_file_path

# Configure logging
logging.basicConfig(level=logging.INFO, format="%(levelname)s: %(message)s")

load_dotenv()

# Constants
Tenant = config.database_name
store = config.store_name
role = config.role

def clean_value(value: Any) -> Any:
    """
    Cleans and converts a value by removing currency, commas, and percent signs,
    and attempts to convert to float. Returns 0 for empty or NaN values.
    """
    if isinstance(value, str):
        value = value.replace("$", "").replace(",", "").replace("%", "").strip()
        try:
            return float(value)
        except ValueError:
            return 0 if value in [None, "nan", "NaN", ""] else value
    return value

def extract_kpis(d: Dict[str, Any]) -> Dict[str, Any]:
    """
    Extracts KPI fields from a nested dictionary, ignoring section headers.
    """
    kpis = {}
    ignored_headers = {
        "A) Financial - Customer Pay",
        "B) Pricing - Customer Pay",
        "C) Volume",
        "D) Opportunities - CP Vehicles Under 60K Miles",
        "E) Opportunities - MPI (CP and Wty)",
        "F) Opportunities - Menu Sales (CP and Wty)",
        "G) Opportunities - CP Vehicles Over 60K Miles",
    }
    for category, sub_dict in d.items():
        if category in ignored_headers:
            continue
        if isinstance(sub_dict, dict):
            for k, v in sub_dict.items():
                kpis[k.strip()] = clean_value(v)
        else:
            kpis[category.strip()] = clean_value(sub_dict)
    return kpis


# Helpers
def sanitize(name): return name.replace(" ", "-")


def get_output_folder(): return f"Playwright-report-{sanitize(Tenant)}_{sanitize(store)}_{sanitize(role)}"


def generate_playwright_style_html(html_path, json_report_data):
    passed = sum(1 for entry in json_report_data if entry['match'])
    failed = len(json_report_data) - passed
    total = len(json_report_data)

    # Group by section prefix
    sectioned_data = defaultdict(list)
    for entry in json_report_data:
        kpi = entry['kpi']
        if ' - ' in kpi:
            section, name = kpi.split(' - ', 1)
        else:
            section, name = 'KPI', kpi
        entry['clean_kpi'] = name
        sectioned_data[section].append(entry)

    html_template = f"""
    <!DOCTYPE html>
    <html lang=\"en\">
    <head>
        <meta charset=\"UTF-8\">
        <title>KPI Dashboard Report</title>
        <link href=\"https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css\" rel=\"stylesheet\">
        <style>
            body {{ padding: 20px; font-family: Arial, sans-serif; }}
            .badge-pass {{ background-color: #28a745; }}
            .badge-fail {{ background-color: #dc3545; }}
            .card-header {{ cursor: pointer; }}
        </style>
    </head>
    <body>
        <div class=\"container\">
            <h1 class=\"mb-4\">KPI Dashboard Report</h1>
            <div class=\"mb-4\">
                <strong>Tenant:</strong> {Tenant}<br>
                <strong>Store:</strong> {store}<br>
                <strong>Role:</strong> {role}<br>
                <strong>Generated At:</strong> {datetime.now().isoformat()}<br>
            </div>

            <div class=\"d-flex gap-3 mb-4\">
                <span class=\"badge bg-success\">Passed: {passed}</span>
                <span class=\"badge bg-danger\">Failed: {failed}</span>
                <span class=\"badge bg-secondary\">Total: {total}</span>
            </div>

            <div class=\"accordion\" id=\"reportAccordion\">
    """

    for s_idx, (section, entries) in enumerate(sectioned_data.items()):
        section_pass = all(entry['match'] for entry in entries)
        badge_class = "badge-pass" if section_pass else "badge-fail"
        badge_text = "Passed" if section_pass else "Failed"
        section_id = f"section{s_idx}"

        html_template += f"""
        <div class=\"accordion-item\">
            <h2 class=\"accordion-header\" id=\"heading-{section_id}\">
                <button class=\"accordion-button collapsed\" type=\"button\" data-bs-toggle=\"collapse\" data-bs-target=\"#{section_id}\" aria-expanded=\"false\" aria-controls=\"{section_id}\">
                    {section} <span class=\"ms-3 badge {badge_class}\">{badge_text}</span>
                </button>
            </h2>
            <div id=\"{section_id}\" class=\"accordion-collapse collapse\" aria-labelledby=\"heading-{section_id}\" data-bs-parent=\"#reportAccordion\">
                <div class=\"accordion-body\">
        """

        for idx, entry in enumerate(entries):
            match = entry['match']
            sub_badge = "badge-pass" if match else "badge-fail"
            sub_text = "Passed" if match else "Failed"
            sub_id = f"{section_id}-entry-{idx}"
            html_template += f"""
            <div class=\"card mb-2\">
                <div class=\"card-header\" data-bs-toggle=\"collapse\" data-bs-target=\"#{sub_id}\" aria-expanded=\"false\" style=\"cursor:pointer;\">
                    {entry['clean_kpi']} <span class=\"ms-2 badge {sub_badge}\">{sub_text}</span>
                </div>
                <div id=\"{sub_id}\" class=\"collapse\">
                    <div class=\"card-body\">
                        <strong>UI:</strong>
                        <pre>{json.dumps(entry['ui'], indent=2)}</pre>
                        <strong>Calculated:</strong>
                        <pre>{json.dumps(entry['calculated'], indent=2)}</pre>
                    </div>
                </div>
            </div>
            """

        html_template += """
                </div>
            </div>
        </div>
        """

    html_template += """
            </div>
        </div>
        <script src=\"https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js\"></script>
    </body>
    </html>
    """

    with open(html_path, "w", encoding="utf-8") as f:
        f.write(html_template)


def compare_dashboard_kpis(
        kpi_dashboard_file,
        result_set_file,
):
    # Load data1 from kpi_dashboard.md
    with open(kpi_dashboard_file, "r", encoding="utf-8") as f1:
        data1 = json.loads(f1.read())  # Load JSON content

    # Extract "KPI Scorecard" section and process only KPI fields
    data1_kpis = extract_kpis(data1.get("KPI Scorecard", {}))

    # Load data2 from results_set.json
    with open(result_set_file, "r", encoding="utf-8") as f2:
        data2 = json.load(f2)

    # Fix: If data2 is a list, merge all dictionaries into a single dictionary
    data2_combined = {}
    if isinstance(data2, list):
        for entry in data2:  # Loop through each dictionary in the list
            data2_combined.update(entry)  # Merge all key-value pairs
    else:
        data2_combined = data2  # If already a dictionary, use as is

    # Extract KPI values from data2
    data2_kpis = extract_kpis(data2_combined)

    # Compare data1 and data2 using only KPI field names
    comparison_results = []
    # is_match =True
    for key, value1 in data1_kpis.items():
        value2 = data2_kpis.get(key, "MISSING")  # Compare using exact KPI names
        # If value2 is NaN, None, "NaN", "nan", or missing, treat it as 0
        if value2 in [None, "NaN", "nan", "NAN", "", "MISSING"] or (isinstance(value2, float) and np.isnan(value2)):
            value2 = 0
        # Initialize is_match with a default comparison
        is_match = value1 == value2
        # Special handling for "Parts to Labor Ratio"
        if key == "Parts to Labor Ratio":
            if isinstance(value1, str) and " to " in value1:
                value1 = value1.split(" to ")[0].strip()  # Take first part (before "to")
                # Convert value1 to integer if possible
                try:
                    value1 = float(value1)
                except ValueError:
                    pass  # Keep as string if conversion fails
            # Convert value2 to integer if it has ".0" (fractional part is 0)
            try:
                value2_float = float(value2)
                value2 = float(value2_float) if value2_float.is_integer() else value2_float
            except ValueError:
                pass  # Keep as string if conversion fails
            is_match = value1 == value2  # Check if values match
        elif key == "Average Vehicle Age":
            if "years" in str(value1):
                value1 = value1.split("years")[0].strip()  # Remove "years" and trim spaces

            # Convert both values to float for accurate comparison
            try:
                value1 = float(value1)
                value2 = float(value2)
            except ValueError:
                pass  # If conversion fails, keep as string

            is_match = value1 == value2  # Check if values match

        # Special handling for values with slashes like "141/ 9/6" vs. "141 / 9 / 6"
        elif isinstance(value1, str) and isinstance(value2, str) and "/" in value1 and "/" in value2:
            cleaned_value1 = "".join(filter(str.isdigit, value1))  # Remove non-numeric characters
            cleaned_value2 = "".join(filter(str.isdigit, value2))  # Remove non-numeric characters
            is_match = cleaned_value1 == cleaned_value2  # Compare cleaned numeric values

        # Append comparison results to the list
        comparison_results.append([key, value1, value2, is_match])

    # Write comparison results to a CSV file
    folder, csv_file = create_folder_file_path(base_folder_name="Individual_Reports",
                            output_file="comparison_results_kpi_dashboard.csv",
                            tenant_name=config.database_name)
    with open(csv_file, mode="w", newline="", encoding="utf-8") as file:
        writer = csv.writer(file)
        writer.writerow(["Field Name", "Value", "Value", "Match (True/False)"])  # CSV Header
        writer.writerows(comparison_results)  # Write all rows

    print(f"Comparison results saved to {csv_file}")

    # Read the CSV file and create an Excel workbook
    folder, xlsx_file = create_folder_file_path(base_folder_name="Individual_Reports",
                                               output_file="comparison_results_kpi_dashboard_highlighted.xlsx",
                                               tenant_name=config.database_name)

    # Convert CSV to Excel
    wb = openpyxl.Workbook()
    ws = wb.active
    ws.title = "Comparison Results"

    ws.merge_cells(start_row=1, start_column=1, end_row=1, end_column=4)
    ws.cell(row=1, column=1).value = "KPI Dashboard"
    ws.cell(row=1, column=1).font = Font(bold=True, size=14)
    ws.cell(row=1, column=1).alignment = Alignment(horizontal="center", vertical="center")
    # Define background fills
    ui_fill = PatternFill(start_color="D9E1F2", end_color="D9E1F2", fill_type="solid")  # Light blue
    calc_fill = PatternFill(start_color="305496", end_color="305496", fill_type="solid")  # Light green
    # UI Title (row 3, columns 2 to 7)
    ws.merge_cells(start_row=3, start_column=2, end_row=3, end_column=2)
    ui_cell = ws.cell(row=3, column=2, value="UI")
    ui_cell.font = Font(bold=True, size=12)
    ui_cell.alignment = Alignment(horizontal="center", vertical="center")
    ui_cell.fill = ui_fill
    # Apply fill to entire merged range
    for col in range(2, 3):
        ws.cell(row=3, column=col).fill = ui_fill

    # Calculated Title
    ws.merge_cells(start_row=3, start_column=3, end_row=3, end_column=3)
    calc_cell = ws.cell(row=3, column=3, value="Calculated")
    calc_cell.font = Font(bold=True, size=12)
    calc_cell.alignment = Alignment(horizontal="center", vertical="center")
    calc_cell.fill = calc_fill
    # Apply fill to entire merged range
    for col in range(3, 4):
        ws.cell(row=3, column=col).fill = calc_fill
        # Apply fill to entire merged range
        for col in range(3, 4):
            ws.cell(row=3, column=col).fill = calc_fill
    # Read CSV
    with open(csv_file, "r", encoding="utf-8") as file:
        reader = csv.reader(file)
        headers = next(reader)

        # Write headers on row 2 with bold font
        for col_idx, header in enumerate(headers, start=1):
            cell = ws.cell(row=4, column=col_idx, value=header)
            cell.font = Font(bold=True)
            cell.alignment = Alignment(horizontal="center", vertical="center")

            # Write data starting from row 4
        for row_idx, row in enumerate(reader, start=4):
            ws.append(row)

        # Define yellow fill for highlighting
        yellow_fill = PatternFill(start_color="FFFF00", end_color="FFFF00", fill_type="solid")

        # Get column index of "Match (True/False)" (assumed to be in the last column)
        match_column_index = ws.max_column

        # Apply formatting to rows where "Match (True/False)" is False
        for row in ws.iter_rows(min_row=2, max_row=ws.max_row, min_col=match_column_index, max_col=match_column_index):
            for cell in row:
                if cell.value == "False":  # Check if match is False
                    for cell_to_fill in ws[cell.row]:  # Apply fill to the entire row
                        cell_to_fill.fill = yellow_fill

        # Save the formatted Excel file
        wb.save(xlsx_file)
        print(f"Excel file with highlighted mismatches saved as {xlsx_file}")

    # # Save JSON report
    folder, json_path = create_folder_file_path(base_folder_name="Individual_Reports",
                            output_file="kpi_dashboard.json",
                            tenant_name=config.database_name)
    with open(json_path, "w") as jf:
        json.dump({
            "tenant": Tenant,
            "store": store,
            "role": role,
            "generatedAt": datetime.now().isoformat(),
            "results": comparison_results
        }, jf, indent=2)

    # Save HTML report
    folder, html_path = create_folder_file_path(base_folder_name="Individual_Reports",
                                                output_file="kpi_dashboard.html",
                                                tenant_name=config.database_name)
    json_report_data = []

    # Process comparison rows from KPI section
    for row in comparison_results:
        kpi = row[0]
        match = row[-1]
        json_report_data.append({
            "kpi": kpi,
            "match": match,
            "ui": {
                "Value": row[1]

            },
            "calculated": {
                "Value": row[2]

            }
        })

    generate_playwright_style_html(html_path, json_report_data)

    print(f"Report generated in: {html_path}")

    # Logs
    print(f"✔ JSON report saved to {json_path}")
    print(f"✔ HTML report saved to {html_path}")
