import os
from datetime import datetime
from dateutil.relativedelta import relativedelta
from dotenv import load_dotenv
load_dotenv()
store_id_set = (os.environ.get('store_id')).strip()
# store_id_set = os.getenv('store_id')
print(store_id_set)
s_date_env = os.environ.get('start_date')
e_date_env = os.environ.get('end_date')
fopc_month = os.environ.get('fopc_month')
pre_fopc_month = os.environ.get('pre_fopc_month')

if store_id_set == 'all':
    store_id = 'all'
else:
    if ',' in store_id_set:
        store_id = f"({', '.join(repr(x.strip()) for x in store_id_set.split(','))})"
    else:
        store_id = f"('{store_id_set.strip()}')"

last_month = os.environ.get('last_month')
if last_month is not None:
    last_month_date = datetime.strptime(last_month,"%Y-%m")
    date_ranges = [(last_month_date - relativedelta(months=i)).strftime("%Y-%m") for i in range(3)]
    date_ranges_sql ="({})".format(",".join([f"'{date}'" for date in date_ranges]))

class OpcodeQuerygenerator:
    def generate_query(self):
        sql_query = f"SELECT department,opcode,opcategory,store_id,grid_excluded FROM stateful_cc_physical_rw.ro_opcodes WHERE store_id in {store_id};"
        return sql_query

class payTypeFixedRateUpdateStatus:
    def generate_query(self):
        getOpcodePaytypeUpdateTable = f"SELECT id, opcode, pay_type, start_date, end_date, store_id FROM stateful_cc_physical_rw.fixed_rate_enable_disable_master_log WHERE id IS NOT NULL AND store_id in {store_id};"
        return getOpcodePaytypeUpdateTable

class payTypeFixedRateTable:
    def generate_query(self):
        getPayTypeFixedRate = f"SELECT id, paytype, labor_fixedratevalue, parts_fixedratevalue, store_id, fixedratedate FROM stateful_cc_physical_rw.fixed_rate_master_paytype WHERE store_id in {store_id};"
        return getPayTypeFixedRate
class opcodeFixedRateTable:
    def generate_query(self):
        getOpcodeFixedRate = f"SELECT id, opcode, paytype, fixed_rate_value, store_id, fixed_rate_date FROM stateful_cc_physical_rw.fixed_rate_master WHERE store_id in {store_id};"
        return getOpcodeFixedRate
class gridDataTable:
    def generate_query(self):
        getGridDataTable = f"SELECT hours, col_0, col_1, col_2, col_3, col_4, col_5, col_6, col_7, col_8, col_9, store_id, created_date, door_rate, grid_type FROM stateful_cc_physical_rw.griddata_dtl WHERE store_id in {store_id};"
        return getGridDataTable
class OpcodeTableQuery:
    def generate_query(self):
        sql_query = f"SELECT department,opcode,opcategory,grid_excluded,store_id, mpi_item FROM stateful_cc_physical_rw.ro_opcodes WHERE store_id in {store_id};"
        return sql_query
class partInventoryDetailsQuery:
    def generate_query(self):
        getPartInventoryDetailsTable = f"SELECT id,partno,part_source,list_price,store_id  FROM stateful_atm_source_raw.parts_inventory_details WHERE store_id in {store_id};"
        return getPartInventoryDetailsTable
class MenuOpcodes:
    def get_menu_opcodes(self):
        sql_query = f"SELECT opcode FROM stateful_cc_physical_rw.menu_opcodes where store_id in {store_id};"
        return sql_query
class payTypeMasterTableQuery:
    def generate_query(self):
        sql_query = f"SELECT pay_type,department,store_id FROM stateful_cc_physical_rw.pay_type_master WHERE store_id in {store_id};"
        return sql_query
class menuMasterTableQuery:
    def generate_query(self):
        sql_query = f"SELECT * FROM stateful_cc_physical_rw.menu_master WHERE store_id in {store_id};"
        return sql_query
class menuServiceTypeTableQuery:
    def generate_query(self):
        sql_query = f"SELECT * FROM stateful_cc_physical_rw.menu_service_type WHERE store_id in {store_id};"
        return sql_query
class assignedMenuModelsTableQuery:
    def generate_query(self):
        sql_query = f"SELECT * FROM stateful_cc_physical_rw.assigned_menu_models WHERE store_id in {store_id};"
        return sql_query
class assignedMenuOpcodesTableQuery:
    def generate_query(self):
        sql_query = f"SELECT * FROM stateful_cc_physical_rw.assigned_menu_opcodes WHERE store_id in {store_id};"
        return sql_query
class MPISetupTableQuery:
    def generate_query(self):
        sql_query = f"SELECT frh,is_active FROM stateful_cc_physical_rw.mpi_setup WHERE store_id in {store_id};"
        return sql_query
class MPIOpcodesTableQuery:
    def generate_query(self):
        sql_query = f"SELECT opcode FROM stateful_cc_physical_rw.mpi_opcodes WHERE store_id in {store_id};"
        return sql_query
class fleetCustomerFixedRateTableQuery:
    def generate_query(self):
        sql_query = f"SELECT * FROM stateful_cc_physical_rw.labor_grid_fleet_account WHERE store_id in {store_id};"
        return sql_query
class fleetPayTypeFixedRateTableQuery:
    def generate_query(self):
        sql_query = f"SELECT * FROM stateful_cc_physical_rw.labor_grid_fleet_paytype WHERE store_id in {store_id};"
        return sql_query
class fleetOpcodeFixedRateTableQuery:
    def generate_query(self):
        sql_query = f"SELECT * FROM stateful_cc_physical_rw.labor_grid_fleet_opcode WHERE store_id in {store_id};"
        return sql_query
class gridDataDetailsQuery:
    def generate_query(self):
        sql_query = f"SELECT * FROM stateful_cc_physical_rw.griddata_dtl WHERE store_id in {store_id};"
        return sql_query
class allRevenueDetailsTableQuery:
    def generate_query(self):
        sql_query = f"SELECT * FROM stateful_cc_aggregate.all_revenue_details WHERE store_id in {store_id} and closeddate >= '{s_date_env}' and closeddate <= '{e_date_env}';"
        return sql_query

if store_id == 'all':
    class allRevenueDetailsForClientReportCardQuery:
        def generate_query(self):
            sql_query = f"SELECT * FROM stateful_cc_aggregate.all_revenue_details WHERE month_year IN ('{fopc_month}' ,'{pre_fopc_month}');"
            return sql_query
else:
    class allRevenueDetailsForClientReportCardQuery:
        def generate_query(self):
            sql_query = f"SELECT * FROM stateful_cc_aggregate.all_revenue_details WHERE  store_id in {store_id} and month_year IN ('{fopc_month}' ,'{pre_fopc_month}');"
            return sql_query

if store_id == 'all':
    class allRevenueDetailsForClientReportCard3MonthQuery:
        def generate_query(self):
            sql_query = f"""SELECT * FROM stateful_cc_aggregate.all_revenue_details WHERE ((month_year >= '{s_date_env}' AND month_year <= '{e_date_env}') OR month_year IN {date_ranges_sql});"""
            return sql_query
else:
    class allRevenueDetailsForClientReportCard3MonthQuery:
        def generate_query(self):
            sql_query = f"""SELECT * FROM stateful_cc_aggregate.all_revenue_details WHERE store_id in {store_id} AND ((month_year >= '{s_date_env}' AND month_year <= '{e_date_env}') OR month_year IN {date_ranges_sql});"""
            return sql_query
