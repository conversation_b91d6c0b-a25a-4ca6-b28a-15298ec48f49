"""
Main script to run all validation scripts for FOPC QA automation.
Parses command-line arguments, validates configuration, and executes all validation modules.
"""

import os
import time
import importlib.util
import sys
import argparse
import logging
from dotenv import load_dotenv
from lib.pattern.report_generator import combine_all_reports
from lib.pattern.config import config
from datetime import datetime
import re
from lib.pattern.sampackag.db_handler.data_loader import RobustDataLoader

# Configure logging
logging.basicConfig(level=logging.INFO, format="%(levelname)s: %(message)s")

load_dotenv()

VALIDATION_FOLDER = os.path.join(os.path.dirname(__file__), "sampackag")


def run_validation_script(filepath: str) -> None:
    """
    Dynamically imports and runs the 'run_validation' function from the given script.
    """
    module_name = os.path.splitext(os.path.basename(filepath))[0]
    spec = importlib.util.spec_from_file_location(module_name, filepath)
    module = importlib.util.module_from_spec(spec)
    sys.modules[module_name] = module
    spec.loader.exec_module(module)

    if hasattr(module, "run_validation"):
        start_time = time.time()
        logging.info(f"Started running: {module_name}")
        module.run_validation()
        end_time = time.time()
        logging.info(f"Completed: {module_name} | Time taken: {end_time - start_time:.2f} seconds")
    else:
        logging.warning(f"No 'run_validation' found in {module_name}")


def run_all_validations(args_dict: dict) -> None:
    """
    Sets global config and runs all validation scripts in the validation folder.
    """
    # Set global config attributes
    config.store_id = args_dict["store_id"]
    config.store_name = args_dict["store_name"]
    config.start_date = args_dict["start_date"]
    config.end_date = args_dict["end_date"]
    config.fopc_month = args_dict["fopc_month"]
    config.pre_fopc_month = args_dict["pre_fopc_month"]
    config.database_name = args_dict["database_name"]
    config.working_days = float(args_dict["working_days"])
    config.advisor = args_dict["advisor"]
    config.technician = args_dict["technician"]
    config.site_url = args_dict["site_url"]
    config.last_month = args_dict["last_month"]
    config.role = args_dict["role"]

    # Load database data once and assign
    loader = RobustDataLoader(max_workers=5, max_retries=2)
    loader.load_all_data_with_retry()
    timings = loader.get_task_timings()
    print(f"Slowest task: {max(timings.items(), key=lambda x: x[1])}")

    logging.info(f"Scanning folder: {VALIDATION_FOLDER}")
    for filename in os.listdir(VALIDATION_FOLDER):
        if filename.startswith("validate_") and filename.endswith(".py"):
            filepath = os.path.join(VALIDATION_FOLDER, filename)
            logging.info(f"Running {filename} with args: {args_dict}")
            run_validation_script(filepath)


def validate_config(config_obj) -> None:
    """
    Validates the configuration object for required formats and values.
    Exits the program if validation fails.
    """
    errors = []

    # Validate date formats
    try:
        start = datetime.strptime(config_obj.start_date, "%Y-%m-%d")
    except ValueError:
        errors.append(
            f"Invalid start_date '{config_obj.start_date}'. Expected format: YYYY-MM-DD and valid calendar date."
        )

    try:
        end = datetime.strptime(config_obj.end_date, "%Y-%m-%d")
    except ValueError:
        errors.append(
            f"Invalid end_date '{config_obj.end_date}'. Expected format: YYYY-MM-DD and valid calendar date."
        )

    if "start" in locals() and "end" in locals() and start > end:
        errors.append("start_date cannot be after end_date.")

    for field_name in ["fopc_month", "pre_fopc_month", "last_month"]:
        value = getattr(config_obj, field_name)
        try:
            datetime.strptime(value, "%Y-%m")
        except ValueError:
            errors.append(f"Invalid {field_name} format. Expected YYYY-MM.")

    # Validate site_url format
    if not re.match(r"^https?://", config_obj.site_url):
        errors.append("site_url must start with http:// or https://")

    if errors:
        for error in errors:
            logging.error(error)
        sys.exit(1)


def main():
    """
    Main entry point for running all validations.
    """
    parser = argparse.ArgumentParser(
        description="Run all validation scripts with store context."
    )

    parser.add_argument("--store_id", required=True, help="Store ID")
    parser.add_argument("--store_name", required=True, help="Store Name")
    parser.add_argument("--start_date", required=True, help="Start date (YYYY-MM-DD)")
    parser.add_argument("--end_date", required=True, help="End date (YYYY-MM-DD)")
    parser.add_argument(
        "--fopc_month",
        required=True,
        help="Current FOPC month being evaluated (format: YYYY-MM)",
    )
    parser.add_argument(
        "--pre_fopc_month",
        required=True,
        help="Previous FOPC month for comparison (format: YYYY-MM)",
    )
    parser.add_argument(
        "--database_name",
        required=True,
        help="Name of the database to connect (e.g., sampackag)",
    )
    parser.add_argument(
        "--working_days",
        required=True,
        help="Number of working days in the selected date range (e.g., 46.74)",
    )
    parser.add_argument(
        "--advisor", required=True, help="Advisor name or 'all' to include all advisors"
    )
    parser.add_argument(
        "--technician",
        required=True,
        help="Technician name or 'all' to include all technicians",
    )
    parser.add_argument(
        "--site_url",
        required=True,
        help="Base URL of the site (e.g., https://sampackag.fixedops.cc/)",
    )
    parser.add_argument(
        "--last_month",
        required=True,
        help="Last month for client report card three months (format: YYYY-MM)",
    )
    parser.add_argument("--role", required=True, help="Name of role")


    args = parser.parse_args()
    args_dict = vars(args)

    validate_config(args)
    total_start = time.time()
    run_all_validations(args_dict)
    total_end = time.time()
    logging.info(f"All validations completed in {total_end - total_start:.2f} seconds")
    combine_all_reports()


if __name__ == "__main__":
    main()
