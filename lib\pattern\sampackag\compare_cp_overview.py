"""
Compares CP Overview UI and calculated results, and generates CSV, Excel, JSON, and HTML reports.
"""

import json
import csv
import os
import re
import logging
import traceback
from openpyxl.styles import Font, Alignment, PatternFill
from openpyxl import Workbook
import openpyxl
from datetime import datetime
from dotenv import load_dotenv
from lib.pattern.config import config
from lib.std.universal.utils import create_folder_file_path
from collections import defaultdict

load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO, format="%(levelname)s: %(message)s")

# Constants
Tenant = config.

store = config.store_name
role = config.role


def clean_number(value):
    """Remove formatting and convert to float if possible."""
    if value is None or value == "":
        return "Missing"
    if isinstance(value, str):
        value = re.sub(r"[$%,]", "", value)  # Remove $, %, and ,
        if re.match(r"\(.*\)", value):  # Check if value is in parentheses (negative number)
            value = "-" + value.strip("()")  # Convert (2) to -2
    try:
        return float(value)
    except ValueError:
        return value  # Return as is if not convertible


def extract_ui_values_from_chart_processing(ui_data):
    """Extract UI values and chart details from chart processing results"""
    ui_values = {}
    chart_details = {}

    try:
        if not isinstance(ui_data, list):
            logging.error(f"Expected UI data to be a list, got {type(ui_data)}")
            return {}, {}

        logging.info(f"Processing {len(ui_data)} UI charts")

        for chart_idx, chart in enumerate(ui_data):
            try:
                if not isinstance(chart, dict):
                    logging.warning(f"Chart {chart_idx} is not a dictionary, skipping")
                    continue

                chart_title = chart.get('chart_title', 'Unknown Chart')
                chart_id = chart.get('chart_id', 'Unknown ID')
                chart_name_with_id = f"{chart_title}({chart_id})"
                target_month = chart.get('target_month_year', 'Unknown')
                dataset_label = chart.get("dataset_label", "Unknown Line")

                # Extract line data point value
                point_data = chart.get("point_data", {})
                line_value = point_data.get("value", "0")
                
                # Handle percentage values
                if "%" in dataset_label:
                    ui_line_value = float(line_value) * 100
                else:
                    ui_line_value = float(line_value)

                # Store line value
                line_key = f"{dataset_label} ({target_month})"
                ui_values[line_key] = {
                    "line_value": clean_number(ui_line_value),
                    "drilldown_values": {}
                }

                # Extract drilldown values from mui_grid_data
                extracted_items = chart.get("extracted_data", {}).get("extraction_data", {}).get("mui_grid_data", [])
                
                for grid in extracted_items:
                    if not isinstance(grid, dict):
                        continue
                    
                    # Only process container_index 0 or 1
                    if grid.get("container_index") in [0, 1]:
                        for item in grid.get("items", []):
                            if not isinstance(item, dict):
                                continue

                            title = item.get("title", "")
                            value = item.get("value", "")

                            if not title or not value:
                                continue

                            clean_value = str(value).replace("$", "").replace(",", "").replace("%", "").strip()
                            drilldown_key = f"{title} ({target_month})"
                            
                            ui_values[line_key]["drilldown_values"][drilldown_key] = clean_number(clean_value)

                # Store chart details
                chart_details[line_key] = {
                    'chart_title': chart_title,
                    'chart_id': chart_id,
                    'chart_name_with_id': chart_name_with_id,
                    'dataset_label': dataset_label,
                    'target_month': target_month
                }

            except Exception as e:
                logging.error(f"Error processing chart {chart_idx}: {e}")
                continue

        logging.info(f"Extracted {len(ui_values)} UI chart values")
        return ui_values, chart_details

    except Exception as e:
        logging.error(f"Error extracting UI values: {e}")
        return {}, {}


def extract_db_values_from_cp_overview(db_data):
    """Extract DB values from CP overview calculated results"""
    db_values = {}
    monthly_data = {}

    try:
        if not isinstance(db_data, dict):
            logging.error(f"Expected DB data to be a dictionary, got {type(db_data)}")
            return {}, {}

        # Extract target_month_results section
        target_month_results = db_data.get('target_month_results', {})
        analysis_info = db_data.get('analysis_info', {})
        
        if not target_month_results:
            logging.error("No target_month_results found in DB data")
            return {}, {}

        # Get target month for date formatting
        target_month = analysis_info.get('target_month', '2023-11-01')
        formatted_date = target_month

        logging.info(f"Target month: {target_month}, Processing DB values for date: {formatted_date}")

        # Get customer_pay_metrics for specific fields
        customer_pay_metrics = target_month_results.get("customer_pay_metrics", {})

        # Create monthly_data structure for line comparison
        monthly_data[formatted_date] = {
            "Labor Revenue": target_month_results.get("labor_revenue", 0),
            "Parts Revenue": target_month_results.get("parts_revenue", 0),
            "Combined Revenue": target_month_results.get("combined_revenue", 0),
            "Labor Gross Profit": target_month_results.get("labor_gross_profit", 0),
            "Parts Gross Profit": target_month_results.get("parts_gross_profit", 0),
            "Combined Gross Profit": target_month_results.get("combined_gross_profit", 0),
            "Labor Gross Profit %": target_month_results.get("labor_gross_profit_percentage", 0),
            "Parts Gross Profit %": target_month_results.get("parts_gross_profit_percentage", 0),
            "Combined Gross Profit %": target_month_results.get("combined_gross_profit_percentage", 0),
            "Labor Sold Hours": target_month_results.get("labor_sold_hours", 0),
            "Effective Labor Rate": customer_pay_metrics.get("effective_labor_rate", 0),
            "Parts Markup": customer_pay_metrics.get("parts_markup", 0)
        }

        # Create drilldown values mapping
        drilldown_mappings = {
            f"Labor Sale - Customer Pay ({formatted_date})": customer_pay_metrics.get("labor_sale_customer_pay", 0),
            f"Total Parts Sales ({formatted_date})": customer_pay_metrics.get("total_parts_sale", 0),
            f"Combined Revenue ({formatted_date})": target_month_results.get("combined_revenue", 0),
            f"Labor Gross Profit ({formatted_date})": target_month_results.get("labor_gross_profit", 0),
            f"Parts Gross Profit ({formatted_date})": target_month_results.get("parts_gross_profit", 0),
            f"Combined Gross Profit ({formatted_date})": target_month_results.get("combined_gross_profit", 0),
            f"Labor Gross Profit % ({formatted_date})": target_month_results.get("labor_gross_profit_percentage", 0),
            f"Parts Gross Profit % ({formatted_date})": target_month_results.get("parts_gross_profit_percentage", 0),
            f"Combined Gross Profit % ({formatted_date})": target_month_results.get("combined_gross_profit_percentage", 0),
            f"Labor Sold Hours ({formatted_date})": target_month_results.get("labor_sold_hours", 0),
            f"Total Parts Cost ({formatted_date})": customer_pay_metrics.get("total_parts_cost", 0),
            f"Total Labor Cost ({formatted_date})": customer_pay_metrics.get("total_labor_cost", 0),
            f"Effective Labor Rate ({formatted_date})": customer_pay_metrics.get("effective_labor_rate", 0),
            f"Parts Markup ({formatted_date})": customer_pay_metrics.get("parts_markup", 0)
        }

        # Structure DB values to match UI format
        for line_name, line_value in monthly_data[formatted_date].items():
            line_key = f"{line_name} ({formatted_date})"
            db_values[line_key] = {
                "line_value": clean_number(line_value),
                "drilldown_values": {}
            }

            # Add relevant drilldown values for this line
            for drilldown_key, drilldown_value in drilldown_mappings.items():
                db_values[line_key]["drilldown_values"][drilldown_key] = clean_number(drilldown_value)

        logging.info(f"Extracted {len(db_values)} DB values from CP overview data")
        return db_values, monthly_data

    except Exception as e:
        logging.error(f"Error extracting DB values: {e}")
        return {}, {}


def compare_values(ui_value, db_value, tolerance=0.01):
    """Compare two values with tolerance for floating point numbers"""
    if ui_value == "Missing" or db_value == "Missing":
        return ui_value == db_value
    
    try:
        ui_float = float(ui_value) if ui_value != "Missing" else 0
        db_float = float(db_value) if db_value != "Missing" else 0
        return abs(ui_float - db_float) < tolerance
    except (ValueError, TypeError):
        return str(ui_value) == str(db_value)


# Helpers
def sanitize(name): return name.replace(" ", "-")
def get_output_folder(): return f"CP-Overview-report-{sanitize(Tenant)}_{sanitize(store)}_{sanitize(role)}"

def generate_playwright_style_html(html_path: str, json_report_data: list) -> None:
    """
    Generates a Bootstrap-styled HTML report from comparison results.
    """
    passed = sum(1 for entry in json_report_data if entry['match'])
    failed = len(json_report_data) - passed
    total = len(json_report_data)

    # Group by chart name
    sectioned_data = defaultdict(list)
    for entry in json_report_data:
        chart_name = entry.get('chart_name_with_id', 'Unknown Chart')
        sectioned_data[chart_name].append(entry)

    html_template = f"""
    <!DOCTYPE html>
    <html lang=\"en\">
    <head>
        <meta charset=\"UTF-8\">
        <title>CP Overview Comparison Report</title>
        <link href=\"https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css\" rel=\"stylesheet\">
        <style>
            body {{ padding: 20px; font-family: Arial, sans-serif; }}
            .badge-pass {{ background-color: #28a745; }}
            .badge-fail {{ background-color: #dc3545; }}
            .card-header {{ cursor: pointer; }}
        </style>
    </head>
    <body>
        <div class=\"container\">
            <h1 class=\"mb-4\">CP Overview Comparison Report</h1>
            <div class=\"mb-4\">
                <strong>Tenant:</strong> {Tenant}<br>
                <strong>Store:</strong> {store}<br>
                <strong>Role:</strong> {role}<br>
                <strong>Generated At:</strong> {datetime.now().isoformat()}<br>
            </div>

            <div class=\"d-flex gap-3 mb-4\">
                <span class=\"badge bg-success\">Passed: {passed}</span>
                <span class=\"badge bg-danger\">Failed: {failed}</span>
                <span class=\"badge bg-secondary\">Total: {total}</span>
            </div>

            <div class=\"accordion\" id=\"reportAccordion\">
    """

    for s_idx, (chart_name, entries) in enumerate(sectioned_data.items()):
        section_pass = all(entry['match'] for entry in entries)
        badge_class = "badge-pass" if section_pass else "badge-fail"
        badge_text = "Passed" if section_pass else "Failed"
        section_id = f"chart{s_idx}"

        html_template += f"""
        <div class=\"accordion-item\">
            <h2 class=\"accordion-header\" id=\"heading-{section_id}\">
                <button class=\"accordion-button collapsed\" type=\"button\" data-bs-toggle=\"collapse\" data-bs-target=\"#{section_id}\" aria-expanded=\"false\" aria-controls=\"{section_id}\">
                    {chart_name} <span class=\"ms-3 badge {badge_class}\">{badge_text}</span>
                </button>
            </h2>
            <div id=\"{section_id}\" class=\"accordion-collapse collapse\" aria-labelledby=\"heading-{section_id}\" data-bs-parent=\"#reportAccordion\">
                <div class=\"accordion-body\">
        """

        for idx, entry in enumerate(entries):
            match = entry['match']
            sub_badge = "badge-pass" if match else "badge-fail"
            sub_text = "Passed" if match else "Failed"
            sub_id = f"{section_id}-entry-{idx}"
            
            html_template += f"""
            <div class=\"card mb-2\">
                <div class=\"card-header\" data-bs-toggle=\"collapse\" data-bs-target=\"#{sub_id}\" aria-expanded=\"false\" style=\"cursor:pointer;\">
                    {entry.get('line_name_legend', 'Unknown')} -> {entry.get('drilldown_field', 'Unknown')} <span class=\"ms-2 badge {sub_badge}\">{sub_text}</span>
                </div>
                <div id=\"{sub_id}\" class=\"collapse\">
                    <div class=\"card-body\">
                        <strong>UI Values:</strong>
                        <pre>{json.dumps(entry['ui'], indent=2)}</pre>
                        <strong>DB Values:</strong>
                        <pre>{json.dumps(entry['db'], indent=2)}</pre>
                    </div>
                </div>
            </div>
            """

        html_template += """
                </div>
            </div>
        </div>
        """

    html_template += """
            </div>
        </div>
        <script src=\"https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js\"></script>
    </body>
    </html>
    """

    with open(html_path, "w", encoding="utf-8") as f:
        f.write(html_template)


def compare_cp_overview_results(ui_json_path, db_json_path):
    """Compare UI chart processing results with DB calculated values for CP Overview"""
    try:
        logging.info("Starting CP Overview comparison...")
        logging.info(f"Loading UI data from: {ui_json_path}")
        logging.info(f"Loading DB data from: {db_json_path}")

        # Load JSON files
        with open(ui_json_path, 'r') as f:
            ui_data = json.load(f)

        with open(db_json_path, 'r') as f:
            db_data = json.load(f)

        # Extract values from both sources
        ui_values, ui_chart_details = extract_ui_values_from_chart_processing(ui_data)
        db_values, db_monthly_data = extract_db_values_from_cp_overview(db_data)

        if not ui_values:
            logging.error("No UI values extracted")
            return []

        if not db_values:
            logging.error("No DB values extracted")
            return []

        comparison_results = []
        json_report_data = []

        # Compare each UI chart with corresponding DB values
        for ui_line_key, ui_line_data in ui_values.items():
            chart_details = ui_chart_details.get(ui_line_key, {})
            chart_name_with_id = chart_details.get('chart_name_with_id', 'Unknown Chart')
            
            # Find corresponding DB line data
            db_line_data = db_values.get(ui_line_key, {"line_value": "Missing", "drilldown_values": {}})
            
            # Compare line values
            ui_line_value = ui_line_data.get("line_value", "Missing")
            db_line_value = db_line_data.get("line_value", "Missing")
            line_match = compare_values(ui_line_value, db_line_value)

            # Compare drilldown values
            ui_drilldowns = ui_line_data.get("drilldown_values", {})
            db_drilldowns = db_line_data.get("drilldown_values", {})
            
            # Create comparison entries for each drilldown field
            for ui_drilldown_key, ui_drilldown_value in ui_drilldowns.items():
                # Try to find matching DB drilldown value
                db_drilldown_value = "Missing"
                for db_drilldown_key, db_drilldown_val in db_drilldowns.items():
                    # Match by field name (ignoring date part for flexibility)
                    ui_field = ui_drilldown_key.split(' (')[0]
                    db_field = db_drilldown_key.split(' (')[0]
                    
                    # Handle field name variations
                    field_mappings = {
                        "Total Parts Sale": "Total Parts Sales",
                        "Effective Labor Rate": "Effective Labor Rate"
                    }
                    
                    if ui_field == db_field or field_mappings.get(ui_field) == db_field:
                        db_drilldown_value = db_drilldown_val
                        break

                drilldown_match = compare_values(ui_drilldown_value, db_drilldown_value)
                
                # Overall match: both line and drilldown must match
                overall_match = line_match and drilldown_match

                # Create comparison result
                result = [
                    chart_name_with_id,
                    ui_line_key,
                    ui_line_value,
                    ui_drilldown_key.split(' (')[0],  # Drilldown field name
                    ui_drilldown_value,
                    db_line_value,
                    db_drilldown_key.split(' (')[0] if db_drilldown_value != "Missing" else "Not Found",
                    db_drilldown_value,
                    overall_match
                ]
                
                comparison_results.append(result)

                # Create JSON report entry
                json_entry = {
                    "chart_name_with_id": chart_name_with_id,
                    "line_name_legend": ui_line_key,
                    "drilldown_field": ui_drilldown_key.split(' (')[0],
                    "match": overall_match,
                    "ui": {
                        "line_value": ui_line_value,
                        "drilldown_value": ui_drilldown_value
                    },
                    "db": {
                        "line_value": db_line_value,
                        "drilldown_value": db_drilldown_value
                    }
                }
                
                json_report_data.append(json_entry)

        # Save comparison results as CSV
        output_folder, output_csv = create_folder_file_path(
            base_folder_name="Individual_Reports",
            output_file="cp_overview_comparison_results.csv",
            tenant_name=config.database_name
        )

        with open(output_csv, "w", newline="", encoding="utf-8") as csvfile:
            writer = csv.writer(csvfile)
            writer.writerow([
                "Chart Name with ID",
                "Line Name (Legend)",
                "UI Line Value",
                "Drilldown Field",
                "UI Drilldown Value",
                "DB Line Value",
                "DB Drilldown Field",
                "DB Drilldown Value",
                "Match (True/False)"
            ])
            writer.writerows(comparison_results)

        logging.info(f"CSV comparison results saved to {output_csv}")

        # Create Excel file with highlighting
        folder, xlsx_file = create_folder_file_path(
            base_folder_name="Individual_Reports",
            output_file="cp_overview_comparison_highlighted.xlsx",
            tenant_name=config.database_name
        )

        # Convert CSV to Excel with formatting
        wb = openpyxl.Workbook()
        ws = wb.active
        ws.title = "CP Overview Comparison"

        # Title
        ws.merge_cells(start_row=1, start_column=1, end_row=1, end_column=9)
        ws.cell(row=1, column=1).value = "CP Overview Comparison Report"
        ws.cell(row=1, column=1).font = Font(bold=True, size=14)
        ws.cell(row=1, column=1).alignment = Alignment(horizontal="center", vertical="center")

        # Section headers
        ui_fill = PatternFill(start_color="D9E1F2", end_color="D9E1F2", fill_type="solid")
        db_fill = PatternFill(start_color="305496", end_color="305496", fill_type="solid")

        ws.merge_cells(start_row=3, start_column=2, end_row=3, end_column=5)
        ui_cell = ws.cell(row=3, column=2, value="UI")
        ui_cell.font = Font(bold=True, size=12)
        ui_cell.alignment = Alignment(horizontal="center", vertical="center")
        ui_cell.fill = ui_fill
        for col in range(2, 6):
            ws.cell(row=3, column=col).fill = ui_fill

        ws.merge_cells(start_row=3, start_column=6, end_row=3, end_column=8)
        db_cell = ws.cell(row=3, column=6, value="DB")
        db_cell.font = Font(bold=True, size=12)
        db_cell.alignment = Alignment(horizontal="center", vertical="center")
        db_cell.fill = db_fill
        for col in range(6, 9):
            ws.cell(row=3, column=col).fill = db_fill

        # Headers
        headers = [
            "Chart Name with ID", "Line Name (Legend)", "UI Line Value",
            "Drilldown Field", "UI Drilldown Value", "DB Line Value",
            "DB Drilldown Field", "DB Drilldown Value", "Match (True/False)"
        ]

        for col_idx, header in enumerate(headers, start=1):
            cell = ws.cell(row=4, column=col_idx, value=header)
            cell.font = Font(bold=True)
            cell.alignment = Alignment(horizontal="center", vertical="center")

        # Data
        for row_idx, row_data in enumerate(comparison_results, start=5):
            for col_idx, value in enumerate(row_data, start=1):
                ws.cell(row=row_idx, column=col_idx, value=value)

        # Highlight mismatches
        yellow_fill = PatternFill(start_color="FFFF00", end_color="FFFF00", fill_type="solid")
        match_column_index = 9  # "Match (True/False)" column

        for row in ws.iter_rows(min_row=5, max_row=ws.max_row, min_col=match_column_index, max_col=match_column_index):
            for cell in row:
                if cell.value == False:  # Check if match is False
                    for cell_to_fill in ws[cell.row]:
                        cell_to_fill.fill = yellow_fill

        wb.save(xlsx_file)
        logging.info(f"Excel file with highlighted mismatches saved as {xlsx_file}")

        # Save JSON report
        folder, json_path = create_folder_file_path(
            base_folder_name="Individual_Reports",
            output_file="cp_overview_comparison.json",
            tenant_name=config.database_name
        )

        with open(json_path, "w") as jf:
            json.dump({
                "tenant": Tenant,
                "store": store,
                "role": role,
                "generatedAt": datetime.now().isoformat(),
                "results": json_report_data
            }, jf, indent=2)

        logging.info(f"JSON report saved to {json_path}")

        # Save HTML report
        folder, html_path = create_folder_file_path(
            base_folder_name="Individual_Reports",
            output_file="cp_overview_comparison.html",
            tenant_name=config.database_name
        )

        generate_playwright_style_html(html_path, json_report_data)
        logging.info(f"HTML report saved to {html_path}")

        logging.info("CP Overview comparison completed successfully")
        return comparison_results

    except Exception as e:
        logging.error(f"Error in CP Overview comparison: {e}")
        traceback.print_exc()
        return []


if __name__ == "__main__":
    # Set default config values when running standalone
    if config.database_name is None:
        config.database_name = "default_tenant"
    if config.store_name is None:
        config.store_name = "default_store"
    if config.role is None:
        config.role = "default_role"

    # Default file paths
    ui_json_path = 'chart_processing_results/chart_processing_all.json'
    db_json_path = 'chart_processing_results/db_calculated_value.json'

    # Run comparison
    results = compare_cp_overview_results(ui_json_path, db_json_path)

    if results:
        print(f"Comparison completed successfully. Generated {len(results)} comparison results.")
    else:
        print("Comparison failed or no results generated.")