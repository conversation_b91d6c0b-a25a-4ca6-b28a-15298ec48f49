import os
import json
import asyncio
from pyzerox import zerox
from dotenv import load_dotenv
from .image_to_pdf import convert_image_to_pdf
from lib.pattern.config import config
from lib.std.universal.utils import create_folder_file_path

load_dotenv()

# Load environment variables
MODEL = os.getenv("GEMINI_MODEL", "gemini/gemini-2.0-flash")
API_KEY = os.getenv("GEMINI_API_KEY")
OUTPUT_DIR = create_folder_file_path(base_folder_name="Omni_Results",
                                tenant_name=config.database_name)


# Ensure API key is set
if not API_KEY:
    raise ValueError("GEMINI_API_KEY is not set in the environment variables.")

os.environ["GEMINI_API_KEY"] = API_KEY

# Placeholder for additional model kwargs which might be required for some models
kwargs = {}


def extract_image_data(custom_system_prompt, image_path, select_pages=None):
    """Processes a PDF file using zerox and returns the result."""

    async def run_zerox():
        pdf_path = os.path.splitext(image_path)[0] + ".pdf"
        file_path = convert_image_to_pdf(image_path, pdf_path)
        return await zerox(
            file_path=file_path,
            model=MODEL,
            output_dir=OUTPUT_DIR,
            custom_system_prompt=custom_system_prompt,
            select_pages=select_pages,
            **kwargs
        )

    return asyncio.run(run_zerox())


def extract_image_data_split(custom_system_prompt, image_paths, select_pages=None):
    """Processes multiple images and extracts JSON using zerox."""

    async def run_all():
        tasks = []
        for image_path in image_paths:
            result_path = os.path.splitext(image_path)[0] + ".pdf"
            pdf_path = convert_image_to_pdf(image_path, result_path)
            task = zerox(
                file_path=pdf_path,
                model=MODEL,
                output_dir=OUTPUT_DIR,
                custom_system_prompt=custom_system_prompt,
                select_pages=select_pages
            )
            tasks.append(task)
        return await asyncio.gather(*tasks)

    return asyncio.run(run_all())

if __name__ == "__main__":
    FILE_PATH = os.getenv("FILE_PATH")
    result = extract_image_data(FILE_PATH)
