"""
Validation script for Client Report Card (1 month).
Automates UI, extracts data, compares with DB, and generates validation reports.
"""

import os
import json
import logging
from decimal import Decimal, ROUND_HALF_UP
from datetime import datetime
from typing import Any, Dict

import pandas as pd
from dotenv import load_dotenv
from playwright.sync_api import sync_playwright

from lib.std.universal.extract_image_data import extract_image_data
from lib.pattern.sampackag.compare_client_report_card import compare_client_report_card
from lib.pattern.config import config
from lib.std.universal.utils import create_folder_file_path

# Configure logging
logging.basicConfig(level=logging.INFO, format="%(levelname)s: %(message)s")

load_dotenv()

CUSTOM_SYSTEM_PROMPT = (
    "Analyze this document and provide the contents in JSON format. The labels should be correctly mapped as in the image "
    """
    The output should be in the following format
    {
  "Monthly FOPC": "$value",
  "Monthly DMS": "$value",
  "Total": "$value",
  "ROI": "value%",
  "Total Pts & Lbr GP Change": "$value",
  "Repair ELR Change": "$value",
  "KPIs": {
    "RO Count": {
      "Second Month": "value",
      "First Month": "value",
      "Variance": "value"
    },
    "Hours Sold": {
      "Second Month": "value",
      "First Month": "value",
      "Variance": "value"
    },
    "Hours Per RO": {
      "Second Month": "value",
      "First Month": "value",
      "Variance": "value"
    },
    "Total Shop ELR": {
      "Second Month": "$value",
      "First Month": "$value",
      "Variance": "$value"
    },
    "Total Labor GP%": {
      "Second Month": "value%",
      "First Month": "value%",
      "Variance": "value%"
    },
    "Total Labor Sales": {
      "Second Month": "$value",
      "First Month": "$value",
      "Variance": "$value"
    },
    "Total Labor GP": {
      "Second Month": "$value",
      "First Month": "$value",
      "Variance": "$value"
    },
    "Total Parts Sales": {
      "Second Month": "$value",
      "First Month": "$value",
      "Variance": "$value"
    },
    "Total Parts GP": {
      "Second Month": "$value",
      "First Month": "$value",
      "Variance": "$value"
    },
    "Total Parts GP%": {
      "Second Month": "value%",
      "First Month": "value%",
      "Variance": "value%"
    },
    "Total Parts & Labor Sales": {
      "Second Month": "$value",
      "First Month": "$value",
      "Variance": "$value"
    },
    "Total Parts & Labor GP": {
      "Second Month": "$value",
      "First Month": "$value",
      "Variance": "$value"
    }
  },
  "Competitive": 
  {
  "Hours Sold":
  {
  "Second Month": "value",
      "First Month": "value",
      "Variance": "value"
  },
  "ELR":
  {
  "Second Month": "$value",
      "First Month": "$value",
      "Variance": "$value"
  },
  "% of Total Shop Hours":
  {
  "Second Month": "value%",
      "First Month": "value%",
      "Variance": "value%"
  },
  "Total Labor Sale":
  {
  "Second Month": "$value",
      "First Month": "$value",
      "Variance": "$value"
  }
  },
  "Maintenance": 
  {
  "Hours Sold":
  {
  "Second Month": "value",
      "First Month": "value",
      "Variance": "value"
  },
  "ELR":
  {
  "Second Month": "$value",
      "First Month": "$value",
      "Variance": "$value"
  },
  "% of Total Shop Hours":
  {
  "Second Month": "value%",
      "First Month": "value%",
      "Variance": "value%"
  },
  "Total Labor Sale":
  {
  "Second Month": "$value",
      "First Month": "$value",
      "Variance": "$value"
  }
  },
  "Repair": 
  {
  "Hours Sold":
  {
  "Second Month": "value",
      "First Month": "value",
      "Variance": "value"
  },
  "ELR":
  {
  "Second Month": "$value",
      "First Month": "$value",
      "Variance": "$value"
  },
  "% of Total Shop Hours":
  {
  "Second Month": "value%",
      "First Month": "value%",
      "Variance": "value%"
  },
  "Total Labor Sale":
  {
  "Second Month": "$value",
      "First Month": "$value",
      "Variance": "$value"
  }
  }
}
    """
)

def round_off(n: float, decimals: int = 0) -> float:
    """Round a number to a given number of decimal places using ROUND_HALF_UP."""
    multiplier = Decimal(10) ** decimals
    if isinstance(n, float):
        n = Decimal(str(n))
    return float((n * multiplier).quantize(Decimal("1"), rounding=ROUND_HALF_UP) / multiplier)

def convert_month_format(date_str: str) -> str:
    """Convert YYYY-MM to Mon-YY format (e.g., '2024-10' -> 'Oct-24')."""
    dt = datetime.strptime(date_str, "%Y-%m")
    role_format = dt.strftime("%b-%y")  # e.g., Oct-24
    #fill_format = dt.strftime("%m/%y")  # e.g., 10/24
    return role_format

def automate_site() -> None:
    """
    Automate the UI to capture Client Report Card screenshot.
    """
    # Get base URL from .env
    base_url = config.site_url
    path = "OneMonthReport"
    site_url = f"{base_url.rstrip('/')}/{path}"
    username = os.getenv("fopc_username")
    password = os.getenv("password")
    result_folder = os.getenv("RESULT_FOLDER")
    fopc_month = convert_month_format(config.fopc_month)
    pre_fopc_month = convert_month_format(config.pre_fopc_month)

    with sync_playwright() as p:
        browser = p.chromium.launch(headless=False)
        context = browser.new_context(
            viewport={"width": 1920, "height": 1000},
            device_scale_factor=2  # Increase this to 3 or 4 for higher screenshot clarity
        )
        page = context.new_page()

        # Step 1: home
        page.goto(site_url)
        page.click("button#login", force=True)
        # Step 2: Login
        page.fill("input[name='username']", username)
        page.fill("input[name='password']", password)
        page.click("input#kc-login")
        # page.wait_for_url("**/ThreeMonthReport", timeout=15000)
        # After login is completed and page is ready
        page.wait_for_selector("#store-select", timeout=10000)
        page.click("#store-select")

        # Wait for dropdown to appear and select the option
        page.get_by_role("option", name=config.store_name).click()

        # Step 2: Click "View Dashboard"
        page.wait_for_selector("button:has-text('View Dashboard')", timeout=10000)
        page.click("button#login")
        page.goto(site_url)
         # Step 3: Select date range
        page.click("#mui-component-select-measured-mth")
        page.get_by_role("option", name=fopc_month).click()
        page.click("#mui-component-select-prior-mth")
        page.get_by_role("option", name=pre_fopc_month).click()
        page.wait_for_load_state("networkidle")
        page.get_by_label("Competitive").click()
        page.get_by_label("Maintenance").click()
        page.get_by_label("Repair").click()
        page.wait_for_timeout(5000)
        folder, filename = create_folder_file_path(base_folder_name="Omni_Results",
                                                   output_file="client_report_card_1_month.jpg",
                                                   tenant_name=config.database_name)
        page.screenshot(path=filename, full_page=True)
        print(f"Screenshot saved to folder: {folder} - {filename}")

        browser.close()

automate_site()

def run_validation():
    # Get the result folder path from the .env
    result_folder = create_folder_file_path(base_folder_name="Omni_Results", tenant_name=config.database_name)

    if result_folder:
        # Create folder if it doesn't exist
        if not os.path.exists(result_folder):
            os.makedirs(result_folder)
            print(f"Created folder: {result_folder}")
        else:
            print(f"Folder already exists: {result_folder}")

    # Get the required environment variables
    storeid = config.store_id
    realm = config.database_name
    dms_fees = Decimal(os.environ.get("dms_fees", "0"))
    fopc_fees = Decimal(os.environ.get("fopc_fees", "0"))
    fopc_month = config.fopc_month
    pre_fopc_month = config.pre_fopc_month


    # Fetching data from DB
    retail_flag_all = config.retail_flag_all
    retail_flag = set(retail_flag_all['source_paytype'])

    # Fetching data from DB
    all_revenue_details_df = config.all_revenue_details_for_client_report_card_3_month

    # Initializing new data frame to filter only required advisor and technician
    filtered_df = all_revenue_details_df[
        (all_revenue_details_df['department'] == 'Service') &
        (all_revenue_details_df['opcategory'] != 'N/A') &
        (all_revenue_details_df['opcategory'].isin(['REPAIR', 'COMPETITIVE', 'MAINTENANCE'])) &
        (all_revenue_details_df['hide_ro'] != True)
        & (all_revenue_details_df['store_id'].astype(str).str.strip() == storeid)
        ]
    filtered_df["store_id"] = filtered_df["store_id"].astype(str)

    merged_df = filtered_df.merge(
            retail_flag_all,
            left_on=['paytypegroup', 'store_id'],
            right_on=['source_paytype', 'store_id'],
            how='left'
        )

    merged_df = merged_df.copy()  # Create a deep copy of filtered_df to avoid the warning
    # RO number with different closeddate will be considered as different RO, joining RO number and closeddate to find out the unique RO number
    merged_df["unique_ro_number"] = merged_df["ronumber"].astype(str) + "_" + merged_df["closeddate"].astype(str)

    # Define customer and warranty pay types dynamically
    if "C" in retail_flag and not "E" in retail_flag and not "M" in retail_flag:
        customer_pay_types = {"C"}
        warranty_pay_types = {"W", "F", "M", "E"}
    elif "C" in retail_flag and not "E" in retail_flag and "M" in retail_flag:
        customer_pay_types = {"C", "M"}
        warranty_pay_types = {"W", "F", "E"}
    elif "C" in retail_flag and "E" in retail_flag and not "M" in retail_flag:
        customer_pay_types = {"C", "E"}
        warranty_pay_types = {"W", "F", "M"}
    elif "C" in retail_flag and "E" in retail_flag and "M" in retail_flag:
        customer_pay_types = {"C", "E", "M"}
        warranty_pay_types = {"W", "F"}

    list_of_paytypegroup_C = merged_df[merged_df["paytypegroup"].isin(customer_pay_types)].to_dict("records")
    # Coverting it to data frame
    total_CP_revenue_details_df = pd.DataFrame(list_of_paytypegroup_C)

    total_CP_revenue_details_df = total_CP_revenue_details_df[
        ~(
            (total_CP_revenue_details_df["lbrsale"].fillna(0) == 0)
            & (total_CP_revenue_details_df["lbrsoldhours"].fillna(0) == 0)
            & (total_CP_revenue_details_df["prtextendedsale"].fillna(0) == 0)
            & (total_CP_revenue_details_df["prtextendedcost"].fillna(0) == 0)
        )
    ]

    after_FOPC_total_revenue_CP = total_CP_revenue_details_df[total_CP_revenue_details_df["month_year"] == fopc_month]
    before_FOPC_total_revenue_CP = total_CP_revenue_details_df[total_CP_revenue_details_df["month_year"] == pre_fopc_month ]
    #'2023-11'
    after_FOPC_ro_count = 0
    after_FOPC_sold_hours = 0
    after_FOPC_hours_per_RO = 0
    after_FOPC_ELR = 0
    after_FOPC_labor_GP_perc = 0
    after_FOPC_labor_sale = 0
    after_FOPC_labor_GP = 0
    after_FOPC_parts_sale = 0
    after_FOPC_parts_GP = 0
    after_FOPC_parts_GP_perc = 0
    after_FOPC_parts_labor_sale = 0
    after_FOPC_parts_labor_GP = 0
    after_FOPC_groupby_category = pd.DataFrame()

    if not after_FOPC_total_revenue_CP.empty:
        # RO Count
        after_FOPC_ro_count = after_FOPC_total_revenue_CP["unique_ro_number"].nunique()
        # Sold Hour calculation
        after_FOPC_sold_hours_value = after_FOPC_total_revenue_CP[
            (after_FOPC_total_revenue_CP["opcategory"] != "N/A")
            & (after_FOPC_total_revenue_CP["opcategory"] != "SHOP SUPPLIES")
        ]["lbrsoldhours"].sum()
        after_FOPC_sold_hours = round_off(after_FOPC_sold_hours_value)
        # Hours per RO
        after_FOPC_hours_per_RO_value = 0
        after_FOPC_hours_per_RO = 0
        if after_FOPC_ro_count != 0:
            after_FOPC_hours_per_RO_value = after_FOPC_sold_hours_value / after_FOPC_ro_count
            after_FOPC_hours_per_RO = round_off(after_FOPC_hours_per_RO_value)
        # ELR
        after_FOPC_labor_sale_value = after_FOPC_total_revenue_CP["lbrsale"].sum()

        after_FOPC_ELR_value = 0
        after_FOPC_ELR = 0
        if after_FOPC_sold_hours_value != 0:
            after_FOPC_ELR_value = after_FOPC_labor_sale_value / after_FOPC_sold_hours_value
            after_FOPC_ELR = round_off(after_FOPC_ELR_value, 2)

        # Labor GP %
        after_FOPC_labor_cost_value = after_FOPC_total_revenue_CP["lbrcost"].sum()
        after_FOPC_labor_GP_value = after_FOPC_labor_sale_value - after_FOPC_labor_cost_value

        after_FOPC_labor_GP_perc = 0
        after_FOPC_labor_GP_perc_value = 0
        if after_FOPC_labor_sale_value != 0:
            after_FOPC_labor_GP_perc_value = (after_FOPC_labor_GP_value / after_FOPC_labor_sale_value) * 100
            after_FOPC_labor_GP_perc = round_off(after_FOPC_labor_GP_perc_value)

        # Labor Sale
        after_FOPC_labor_sale = round_off(after_FOPC_labor_sale_value)

        # Labor GP
        after_FOPC_labor_GP_value = after_FOPC_labor_sale_value - after_FOPC_labor_cost_value
        after_FOPC_labor_GP = round_off(after_FOPC_labor_GP_value)

        # Parts sale
        after_FOPC_parts_sale_value = after_FOPC_total_revenue_CP["prtextendedsale"].sum()
        after_FOPC_parts_sale = round_off(after_FOPC_parts_sale_value)

        # Parts GP
        after_FOPC_parts_cost_value = after_FOPC_total_revenue_CP["prtextendedcost"].sum()
        after_FOPC_parts_GP_value = after_FOPC_parts_sale_value - after_FOPC_parts_cost_value
        after_FOPC_parts_GP = round_off(after_FOPC_parts_GP_value)

        # Parts GP%
        after_FOPC_parts_GP_perc_value = 0
        after_FOPC_parts_GP_perc = 0
        if after_FOPC_parts_sale_value != 0:
            after_FOPC_parts_GP_perc_value = (after_FOPC_parts_GP_value / after_FOPC_parts_sale_value) * 100
            after_FOPC_parts_GP_perc = round_off(after_FOPC_parts_GP_perc_value)

        # Total Parts & Labor Sale
        after_FOPC_parts_labor_sale_value = after_FOPC_parts_sale_value + after_FOPC_labor_sale_value
        after_FOPC_parts_labor_sale = round_off(after_FOPC_parts_labor_sale_value)

        # Total Labor and Parts Gross Profit
        after_FOPC_parts_labor_cost = after_FOPC_labor_cost_value + after_FOPC_parts_cost_value
        after_FOPC_parts_labor_GP_value = after_FOPC_parts_labor_sale_value - after_FOPC_parts_labor_cost
        after_FOPC_parts_labor_GP = round_off(after_FOPC_parts_labor_GP_value)
        after_FOPC_groupby_category = (
            after_FOPC_total_revenue_CP.groupby(["opcategory"]).agg({"lbrsale": "sum", "lbrsoldhours": "sum"}).reset_index()
        )

        for index, rows in after_FOPC_groupby_category.iterrows():
            after_FOPC_groupby_category.at[index, "after_FOPC_ELR"] = 0
            if rows["lbrsoldhours"] != 0:
                after_FOPC_groupby_category.at[index, "ELR"] = float(rows["lbrsale"] / rows["lbrsoldhours"])

            after_FOPC_groupby_category.at[index, "perc_of_total_hours"] = 0
            if after_FOPC_sold_hours_value != 0:
                after_FOPC_groupby_category.at[index, "perc_of_total_hours"] = float(
                    (rows["lbrsoldhours"] / after_FOPC_sold_hours_value) * 100
                )

    # --------------------------------------------------------------------------------------------------

    before_FOPC_ro_count = 0
    before_FOPC_sold_hour = 0
    before_FOPC_hours_per_R = 0
    before_FOPC_EL = 0
    before_FOPC_labor_GP_per = 0
    before_FOPC_labor_sal = 0
    before_FOPC_labor_G = 0
    before_FOPC_parts_sal = 0
    before_FOPC_parts_G = 0
    before_FOPC_parts_GP_per = 0
    before_FOPC_parts_labor_sal = 0
    # before_FOPC_parts_labor_G = 0
    before_FOPC_parts_labor_GP = 0
    if not before_FOPC_total_revenue_CP.empty:
        before_FOPC_ro_count = before_FOPC_total_revenue_CP["unique_ro_number"].nunique()

        before_FOPC_sold_hours_value = before_FOPC_total_revenue_CP[
            (before_FOPC_total_revenue_CP["opcategory"] != "N/A")
            & (before_FOPC_total_revenue_CP["opcategory"] != "SHOP SUPPLIES")
        ]["lbrsoldhours"].sum()
        before_FOPC_sold_hours = round_off(before_FOPC_sold_hours_value)

        before_FOPC_hours_per_RO_value = 0
        before_FOPC_hours_per_RO = 0
        if before_FOPC_ro_count != 0:
            before_FOPC_hours_per_RO_value = before_FOPC_sold_hours_value / before_FOPC_ro_count
            before_FOPC_hours_per_RO = round_off(before_FOPC_hours_per_RO_value)
        # ELR
        before_FOPC_labor_sale_value = before_FOPC_total_revenue_CP["lbrsale"].sum()

        before_FOPC_ELR_value = 0
        before_FOPC_ELR = 0
        if before_FOPC_sold_hours_value != 0:
            before_FOPC_ELR_value = before_FOPC_labor_sale_value / before_FOPC_sold_hours_value
            before_FOPC_ELR = round_off(before_FOPC_ELR_value, 2)

        # Labor GP %
        before_FOPC_labor_cost_value = before_FOPC_total_revenue_CP["lbrcost"].sum()
        before_FOPC_labor_GP_value = before_FOPC_labor_sale_value - before_FOPC_labor_cost_value

        before_FOPC_labor_GP_perc = 0
        before_FOPC_labor_GP_perc_value = 0
        if before_FOPC_labor_sale_value != 0:
            before_FOPC_labor_GP_perc_value = (before_FOPC_labor_GP_value / before_FOPC_labor_sale_value) * 100
            before_FOPC_labor_GP_perc = round_off(before_FOPC_labor_GP_perc_value)

        # Labor Sale
        before_FOPC_labor_sale = round_off(before_FOPC_labor_sale_value)

        # Labor GP
        before_FOPC_labor_GP_value = before_FOPC_labor_sale_value - before_FOPC_labor_cost_value
        before_FOPC_labor_GP = round_off(before_FOPC_labor_GP_value)

        # Parts sale
        before_FOPC_parts_sale_value = before_FOPC_total_revenue_CP["prtextendedsale"].sum()
        before_FOPC_parts_sale = round_off(before_FOPC_parts_sale_value)

        # Parts GP
        before_FOPC_parts_cost_value = before_FOPC_total_revenue_CP["prtextendedcost"].sum()
        before_FOPC_parts_GP_value = before_FOPC_parts_sale_value - before_FOPC_parts_cost_value
        before_FOPC_parts_GP = round_off(before_FOPC_parts_GP_value)

        # Parts GP%
        before_FOPC_parts_GP_perc_value = 0
        before_FOPC_parts_GP_perc = 0
        if before_FOPC_parts_sale_value != 0:
            before_FOPC_parts_GP_perc_value = (before_FOPC_parts_GP_value / before_FOPC_parts_sale_value) * 100
            before_FOPC_parts_GP_perc = round_off(before_FOPC_parts_GP_perc_value)

        # Total Parts & Labor Sale
        before_FOPC_parts_labor_sale_value = before_FOPC_parts_sale_value + before_FOPC_labor_sale_value
        before_FOPC_parts_labor_sale = round_off(before_FOPC_parts_labor_sale_value)

        # Total Labor and Parts Gross Profit
        before_FOPC_parts_labor_cost = before_FOPC_labor_cost_value + before_FOPC_parts_cost_value
        before_FOPC_parts_labor_GP_value = before_FOPC_parts_labor_sale_value - before_FOPC_parts_labor_cost
        before_FOPC_parts_labor_GP = round_off(before_FOPC_parts_labor_GP_value)

        # Opcategory Splitup
        before_FOPC_groupby_category = (
            before_FOPC_total_revenue_CP.groupby(["opcategory"])
            .agg({"lbrsale": "sum", "lbrsoldhours": "sum"})
            .reset_index()
        )

        for index, rows in before_FOPC_groupby_category.iterrows():
            before_FOPC_groupby_category.at[index, "before_FOPC_ELR"] = 0
            if rows["lbrsoldhours"] != 0:
                before_FOPC_groupby_category.at[index, "ELR"] = float(rows["lbrsale"] / rows["lbrsoldhours"])

            before_FOPC_groupby_category.at[index, "perc_of_total_hours"] = 0
            if before_FOPC_sold_hours_value != 0:
                before_FOPC_groupby_category.at[index, "perc_of_total_hours"] = float(
                    (rows["lbrsoldhours"] / before_FOPC_sold_hours_value) * 100
                )

    Client_Report_Card = []

    # ROI Calculations
    Monthly_fees = float(dms_fees + fopc_fees)
    total_lbr_pts_GP_variance = after_FOPC_parts_labor_GP - before_FOPC_parts_labor_GP

    total_pts_lbr_GP_ROI = 0
    if float(Monthly_fees) != 0:
        total_pts_lbr_GP_ROI = round_off(
            ((float(total_lbr_pts_GP_variance) - float(Monthly_fees)) / float(Monthly_fees)) * 100
        )
    # Repair ROI Calculations

    after_FOPC_ELR_REPAIR = round_off(
        (after_FOPC_groupby_category.loc[after_FOPC_groupby_category["opcategory"] == "REPAIR", "ELR"].iloc[0]), 2
    )

    before_FOPC_ELR_REPAIR = round_off(
        (before_FOPC_groupby_category.loc[before_FOPC_groupby_category["opcategory"] == "REPAIR", "ELR"].iloc[0]), 2
    )

    after_FOPC_Hours_Sold = round_off(
        after_FOPC_groupby_category.loc[after_FOPC_groupby_category["opcategory"] == "REPAIR", "lbrsoldhours"].iloc[0]
    )

    ELR_variance_REP = after_FOPC_ELR_REPAIR - before_FOPC_ELR_REPAIR

    FOPC_sales_based_on_ELR_variance = round_off(ELR_variance_REP * after_FOPC_Hours_Sold)

    Repair_ROI = 0

    if float(Monthly_fees) > 0.0:
        Repair_ROI = round_off(((FOPC_sales_based_on_ELR_variance - Monthly_fees) / Monthly_fees) * 100)

    # Initialize output dictionary
    Client_Report_Card = {
        "Monthly FOPC": "$0",
        "Monthly DMS": "$0",
        "Total": "$0",
        "ROI": "0%",
        "Total Pts & Lbr GP Change": "$0",
        "Repair ELR Change": "$0",
        "KPIs": {},
        "Competitive": {},
        "Maintenance": {},
        "Repair": {},
    }
    Client_Report_Card = {
        "Monthly FOPC": float(fopc_fees),
        "Monthly DMS": float(dms_fees),
        "Total": Monthly_fees,
        "ROI": total_pts_lbr_GP_ROI,
        "Total Pts & Lbr GP Change": round_off(total_lbr_pts_GP_variance),
        "Repair ELR Change": int(FOPC_sales_based_on_ELR_variance),
        "KPIs": {
            "RO Count": [after_FOPC_ro_count, before_FOPC_ro_count, after_FOPC_ro_count - before_FOPC_ro_count],
            "Hours Sold": [after_FOPC_sold_hours, before_FOPC_sold_hours, after_FOPC_sold_hours - before_FOPC_sold_hours],
            "Hours Per RO": [
                after_FOPC_hours_per_RO,
                before_FOPC_hours_per_RO,
                after_FOPC_hours_per_RO - before_FOPC_hours_per_RO,
            ],
            "Total Shop ELR": [after_FOPC_ELR, before_FOPC_ELR, round_off(after_FOPC_ELR - before_FOPC_ELR, 2)],
            "Total Labor GP%": [
                after_FOPC_labor_GP_perc,
                before_FOPC_labor_GP_perc,
                after_FOPC_labor_GP_perc - before_FOPC_labor_GP_perc,
            ],
            "Total Labor Sales": [
                after_FOPC_labor_sale,
                before_FOPC_labor_sale,
                after_FOPC_labor_sale - before_FOPC_labor_sale,
            ],
            "Total Labor GP": [after_FOPC_labor_GP, before_FOPC_labor_GP, after_FOPC_labor_GP - before_FOPC_labor_GP],
            "Total Parts Sales": [
                after_FOPC_parts_sale,
                before_FOPC_parts_sale,
                after_FOPC_parts_sale - before_FOPC_parts_sale,
            ],
            "Total Parts GP": [after_FOPC_parts_GP, before_FOPC_parts_GP, after_FOPC_parts_GP - before_FOPC_parts_GP],
            "Total Parts GP%": [
                after_FOPC_parts_GP_perc,
                before_FOPC_parts_GP_perc,
                after_FOPC_parts_GP_perc - before_FOPC_parts_GP_perc,
            ],
            "Total Parts & Labor Sales": [
                after_FOPC_parts_labor_sale,
                before_FOPC_parts_labor_sale,
                after_FOPC_parts_labor_sale - before_FOPC_parts_labor_sale,
            ],
            "Total Parts & Labor GP": [
                after_FOPC_parts_labor_GP,
                before_FOPC_parts_labor_GP,
                after_FOPC_parts_labor_GP - before_FOPC_parts_labor_GP,
            ],
        },
        "Competitive": {},
        "Maintenance": {},
        "Repair": {},
    }


    special_cases = {}
    for opcategory in ["Competitive", "Maintenance", "Repair"]:
        # Ensure opcategory exists in both DataFrames before filtering
        if (
            opcategory.upper() in after_FOPC_groupby_category["opcategory"].str.upper().unique()
            and opcategory.upper() in before_FOPC_groupby_category["opcategory"].str.upper().unique()
        ):
            filtered_after = after_FOPC_groupby_category[
                after_FOPC_groupby_category["opcategory"].str.strip().str.upper() == opcategory.upper()
            ]
            filtered_before = before_FOPC_groupby_category[
                before_FOPC_groupby_category["opcategory"].str.strip().str.upper() == opcategory.upper()
            ]
            elr_variance = round_off(filtered_after['ELR'].iloc[0], 2) - round_off(filtered_before['ELR'].iloc[0], 2)
            # Create the special cases dictionary
            special_cases = {
                "Hours Sold": {
                    "Second Month": round_off(filtered_after["lbrsoldhours"].iloc[0]),
                    "First Month": round_off(filtered_before["lbrsoldhours"].iloc[0]),
                    "Variance": round_off(filtered_after["lbrsoldhours"].iloc[0])
                    - round_off(filtered_before["lbrsoldhours"].iloc[0]),
                },
                # "ELR": {
                #     "Second Month": f"${round_off(filtered_after['ELR'].iloc[0], 2)}",
                #     "First Month": f"${round_off(filtered_before['ELR'].iloc[0], 2)}",
                #     "Variance": f"${round_off(filtered_after['ELR'].iloc[0], 2) - round_off(filtered_before['ELR'].iloc[0], 2)}",
                # },
                "ELR": {
                    "Second Month": f"${round_off(filtered_after['ELR'].iloc[0], 2)}",
                    "First Month": f"${round_off(filtered_before['ELR'].iloc[0], 2)}",
                    "Variance": f"${round_off(elr_variance, 2)}"
                },
                "% of Total Shop Hours": {
                    "Second Month": f"{round_off(filtered_after['perc_of_total_hours'].iloc[0])}%",
                    "First Month": f"{round_off(filtered_before['perc_of_total_hours'].iloc[0])}%",
                    "Variance": f"{round_off(filtered_after['perc_of_total_hours'].iloc[0]) - round_off(filtered_before['perc_of_total_hours'].iloc[0])}%",
                },
                "Total Labor Sale": {
                    "Second Month": f"${round_off(filtered_after['lbrsale'].iloc[0])}",
                    "First Month": f"${round_off(filtered_before['lbrsale'].iloc[0])}",
                    "Variance": f"${round_off(filtered_after['lbrsale'].iloc[0]) - round_off(filtered_before['lbrsale'].iloc[0])}",
                },
            }

            # Update Client_Report_Card properly
            Client_Report_Card[opcategory] = special_cases

            if not filtered_after.empty and not filtered_before.empty:
                # Proceed with calculations
                pass  # Replace with actual logic
            else:
                print(f"No data found for category '{opcategory}'")
        else:
            print(f"'{opcategory}' not found in one or both DataFrames")


    # Build the output path
    json_output_path = os.path.join(result_folder, "client_report_card_1_month.json")
    output_md_file_path = os.path.join(result_folder, "client_report_card_1_month.md")
    with open(json_output_path, "w") as json_file:
        json.dump(Client_Report_Card, json_file, indent=4)

    # extract data from UI screenshot
    image_path = os.path.join(result_folder, "client_report_card_1_month.jpg")
    extract_image_data(CUSTOM_SYSTEM_PROMPT, image_path)
    # compare calculated values and the UI values
    compare_client_report_card(output_md_file_path, json_output_path)

    print("Completed the validation of Client Report Card 1 month")
